{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/boost-smart-ptr-x64-windows-1.88.0-d958d9ed-9b3c-47f6-a910-ddc54094c65b", "name": "boost-smart-ptr:x64-windows@1.88.0 25396aca0569446e752ae3ff2c4d95128a998a61ecab3f85f367cf069f847f48", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-14T04:45:23Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "boost-smart-ptr", "SPDXID": "SPDXRef-port", "versionInfo": "1.88.0", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/boost-smart-ptr", "homepage": "https://www.boost.org/libs/smart_ptr", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Boost smart_ptr module", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "boost-smart-ptr:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "25396aca0569446e752ae3ff2c4d95128a998a61ecab3f85f367cf069f847f48", "downloadLocation": "NONE", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "boostorg/smart_ptr", "downloadLocation": "git+https://github.com/boostorg/smart_ptr@boost-1.88.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "a26ceb061de3a8cc95d85b332572e2ba1d0b4559e447741a27221aa9bcb7ecc739d9238c37385596bc47a8e61488d0e76209d95dad500d5bb39dcd6218c4ff7d"}]}], "files": [{"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "4a5c1cf4b7648fb1b97fd1c883fa1641bfcf779482f012cddbc63fbfd0982e7e"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "75c7116e1c8876365c1c039fe084786f4a1c08f5426b5fe775e1a12234891b6e"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}