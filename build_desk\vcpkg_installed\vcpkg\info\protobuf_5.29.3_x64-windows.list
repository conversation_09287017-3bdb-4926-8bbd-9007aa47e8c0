x64-windows/
x64-windows/bin/
x64-windows/bin/libprotobuf-lite.dll
x64-windows/bin/libprotobuf-lite.pdb
x64-windows/bin/libprotobuf.dll
x64-windows/bin/libprotobuf.pdb
x64-windows/bin/libprotoc.dll
x64-windows/bin/libprotoc.pdb
x64-windows/debug/
x64-windows/debug/bin/
x64-windows/debug/bin/libprotobuf-lited.dll
x64-windows/debug/bin/libprotobuf-lited.pdb
x64-windows/debug/bin/libprotobufd.dll
x64-windows/debug/bin/libprotobufd.pdb
x64-windows/debug/bin/libprotocd.dll
x64-windows/debug/bin/libprotocd.pdb
x64-windows/debug/lib/
x64-windows/debug/lib/libprotobuf-lited.lib
x64-windows/debug/lib/libprotobufd.lib
x64-windows/debug/lib/libprotocd.lib
x64-windows/debug/lib/pkgconfig/
x64-windows/debug/lib/pkgconfig/protobuf-lite.pc
x64-windows/debug/lib/pkgconfig/protobuf.pc
x64-windows/include/
x64-windows/include/google/
x64-windows/include/google/protobuf/
x64-windows/include/google/protobuf/any.h
x64-windows/include/google/protobuf/any.pb.h
x64-windows/include/google/protobuf/any.proto
x64-windows/include/google/protobuf/api.pb.h
x64-windows/include/google/protobuf/api.proto
x64-windows/include/google/protobuf/arena.h
x64-windows/include/google/protobuf/arena_align.h
x64-windows/include/google/protobuf/arena_allocation_policy.h
x64-windows/include/google/protobuf/arena_cleanup.h
x64-windows/include/google/protobuf/arenastring.h
x64-windows/include/google/protobuf/arenaz_sampler.h
x64-windows/include/google/protobuf/compiler/
x64-windows/include/google/protobuf/compiler/code_generator.h
x64-windows/include/google/protobuf/compiler/code_generator_lite.h
x64-windows/include/google/protobuf/compiler/command_line_interface.h
x64-windows/include/google/protobuf/compiler/cpp/
x64-windows/include/google/protobuf/compiler/cpp/enum.h
x64-windows/include/google/protobuf/compiler/cpp/extension.h
x64-windows/include/google/protobuf/compiler/cpp/field.h
x64-windows/include/google/protobuf/compiler/cpp/field_generators/
x64-windows/include/google/protobuf/compiler/cpp/field_generators/generators.h
x64-windows/include/google/protobuf/compiler/cpp/file.h
x64-windows/include/google/protobuf/compiler/cpp/generator.h
x64-windows/include/google/protobuf/compiler/cpp/helpers.h
x64-windows/include/google/protobuf/compiler/cpp/ifndef_guard.h
x64-windows/include/google/protobuf/compiler/cpp/message.h
x64-windows/include/google/protobuf/compiler/cpp/message_layout_helper.h
x64-windows/include/google/protobuf/compiler/cpp/names.h
x64-windows/include/google/protobuf/compiler/cpp/namespace_printer.h
x64-windows/include/google/protobuf/compiler/cpp/options.h
x64-windows/include/google/protobuf/compiler/cpp/padding_optimizer.h
x64-windows/include/google/protobuf/compiler/cpp/parse_function_generator.h
x64-windows/include/google/protobuf/compiler/cpp/service.h
x64-windows/include/google/protobuf/compiler/cpp/tracker.h
x64-windows/include/google/protobuf/compiler/csharp/
x64-windows/include/google/protobuf/compiler/csharp/csharp_doc_comment.h
x64-windows/include/google/protobuf/compiler/csharp/csharp_enum.h
x64-windows/include/google/protobuf/compiler/csharp/csharp_enum_field.h
x64-windows/include/google/protobuf/compiler/csharp/csharp_field_base.h
x64-windows/include/google/protobuf/compiler/csharp/csharp_generator.h
x64-windows/include/google/protobuf/compiler/csharp/csharp_helpers.h
x64-windows/include/google/protobuf/compiler/csharp/csharp_map_field.h
x64-windows/include/google/protobuf/compiler/csharp/csharp_message.h
x64-windows/include/google/protobuf/compiler/csharp/csharp_message_field.h
x64-windows/include/google/protobuf/compiler/csharp/csharp_options.h
x64-windows/include/google/protobuf/compiler/csharp/csharp_primitive_field.h
x64-windows/include/google/protobuf/compiler/csharp/csharp_reflection_class.h
x64-windows/include/google/protobuf/compiler/csharp/csharp_repeated_enum_field.h
x64-windows/include/google/protobuf/compiler/csharp/csharp_repeated_message_field.h
x64-windows/include/google/protobuf/compiler/csharp/csharp_repeated_primitive_field.h
x64-windows/include/google/protobuf/compiler/csharp/csharp_source_generator_base.h
x64-windows/include/google/protobuf/compiler/csharp/csharp_wrapper_field.h
x64-windows/include/google/protobuf/compiler/csharp/names.h
x64-windows/include/google/protobuf/compiler/importer.h
x64-windows/include/google/protobuf/compiler/java/
x64-windows/include/google/protobuf/compiler/java/context.h
x64-windows/include/google/protobuf/compiler/java/doc_comment.h
x64-windows/include/google/protobuf/compiler/java/field_common.h
x64-windows/include/google/protobuf/compiler/java/file.h
x64-windows/include/google/protobuf/compiler/java/full/
x64-windows/include/google/protobuf/compiler/java/full/enum.h
x64-windows/include/google/protobuf/compiler/java/full/enum_field.h
x64-windows/include/google/protobuf/compiler/java/full/extension.h
x64-windows/include/google/protobuf/compiler/java/full/field_generator.h
x64-windows/include/google/protobuf/compiler/java/full/generator_factory.h
x64-windows/include/google/protobuf/compiler/java/full/make_field_gens.h
x64-windows/include/google/protobuf/compiler/java/full/map_field.h
x64-windows/include/google/protobuf/compiler/java/full/message.h
x64-windows/include/google/protobuf/compiler/java/full/message_builder.h
x64-windows/include/google/protobuf/compiler/java/full/message_field.h
x64-windows/include/google/protobuf/compiler/java/full/primitive_field.h
x64-windows/include/google/protobuf/compiler/java/full/service.h
x64-windows/include/google/protobuf/compiler/java/full/string_field.h
x64-windows/include/google/protobuf/compiler/java/generator.h
x64-windows/include/google/protobuf/compiler/java/generator_common.h
x64-windows/include/google/protobuf/compiler/java/generator_factory.h
x64-windows/include/google/protobuf/compiler/java/helpers.h
x64-windows/include/google/protobuf/compiler/java/internal_helpers.h
x64-windows/include/google/protobuf/compiler/java/java_features.pb.h
x64-windows/include/google/protobuf/compiler/java/lite/
x64-windows/include/google/protobuf/compiler/java/lite/enum.h
x64-windows/include/google/protobuf/compiler/java/lite/enum_field.h
x64-windows/include/google/protobuf/compiler/java/lite/extension.h
x64-windows/include/google/protobuf/compiler/java/lite/field_generator.h
x64-windows/include/google/protobuf/compiler/java/lite/generator_factory.h
x64-windows/include/google/protobuf/compiler/java/lite/make_field_gens.h
x64-windows/include/google/protobuf/compiler/java/lite/map_field.h
x64-windows/include/google/protobuf/compiler/java/lite/message.h
x64-windows/include/google/protobuf/compiler/java/lite/message_builder.h
x64-windows/include/google/protobuf/compiler/java/lite/message_field.h
x64-windows/include/google/protobuf/compiler/java/lite/primitive_field.h
x64-windows/include/google/protobuf/compiler/java/lite/string_field.h
x64-windows/include/google/protobuf/compiler/java/message_serialization.h
x64-windows/include/google/protobuf/compiler/java/name_resolver.h
x64-windows/include/google/protobuf/compiler/java/names.h
x64-windows/include/google/protobuf/compiler/java/options.h
x64-windows/include/google/protobuf/compiler/java/shared_code_generator.h
x64-windows/include/google/protobuf/compiler/kotlin/
x64-windows/include/google/protobuf/compiler/kotlin/file.h
x64-windows/include/google/protobuf/compiler/kotlin/generator.h
x64-windows/include/google/protobuf/compiler/kotlin/message.h
x64-windows/include/google/protobuf/compiler/objectivec/
x64-windows/include/google/protobuf/compiler/objectivec/enum.h
x64-windows/include/google/protobuf/compiler/objectivec/enum_field.h
x64-windows/include/google/protobuf/compiler/objectivec/extension.h
x64-windows/include/google/protobuf/compiler/objectivec/field.h
x64-windows/include/google/protobuf/compiler/objectivec/file.h
x64-windows/include/google/protobuf/compiler/objectivec/generator.h
x64-windows/include/google/protobuf/compiler/objectivec/helpers.h
x64-windows/include/google/protobuf/compiler/objectivec/import_writer.h
x64-windows/include/google/protobuf/compiler/objectivec/line_consumer.h
x64-windows/include/google/protobuf/compiler/objectivec/map_field.h
x64-windows/include/google/protobuf/compiler/objectivec/message.h
x64-windows/include/google/protobuf/compiler/objectivec/message_field.h
x64-windows/include/google/protobuf/compiler/objectivec/names.h
x64-windows/include/google/protobuf/compiler/objectivec/nsobject_methods.h
x64-windows/include/google/protobuf/compiler/objectivec/oneof.h
x64-windows/include/google/protobuf/compiler/objectivec/options.h
x64-windows/include/google/protobuf/compiler/objectivec/primitive_field.h
x64-windows/include/google/protobuf/compiler/objectivec/tf_decode_data.h
x64-windows/include/google/protobuf/compiler/parser.h
x64-windows/include/google/protobuf/compiler/php/
x64-windows/include/google/protobuf/compiler/php/names.h
x64-windows/include/google/protobuf/compiler/php/php_generator.h
x64-windows/include/google/protobuf/compiler/plugin.h
x64-windows/include/google/protobuf/compiler/plugin.pb.h
x64-windows/include/google/protobuf/compiler/plugin.proto
x64-windows/include/google/protobuf/compiler/python/
x64-windows/include/google/protobuf/compiler/python/generator.h
x64-windows/include/google/protobuf/compiler/python/helpers.h
x64-windows/include/google/protobuf/compiler/python/pyi_generator.h
x64-windows/include/google/protobuf/compiler/retention.h
x64-windows/include/google/protobuf/compiler/ruby/
x64-windows/include/google/protobuf/compiler/ruby/ruby_generator.h
x64-windows/include/google/protobuf/compiler/rust/
x64-windows/include/google/protobuf/compiler/rust/accessors/
x64-windows/include/google/protobuf/compiler/rust/accessors/accessor_case.h
x64-windows/include/google/protobuf/compiler/rust/accessors/accessors.h
x64-windows/include/google/protobuf/compiler/rust/accessors/default_value.h
x64-windows/include/google/protobuf/compiler/rust/accessors/generator.h
x64-windows/include/google/protobuf/compiler/rust/accessors/with_presence.h
x64-windows/include/google/protobuf/compiler/rust/context.h
x64-windows/include/google/protobuf/compiler/rust/crate_mapping.h
x64-windows/include/google/protobuf/compiler/rust/enum.h
x64-windows/include/google/protobuf/compiler/rust/generator.h
x64-windows/include/google/protobuf/compiler/rust/message.h
x64-windows/include/google/protobuf/compiler/rust/naming.h
x64-windows/include/google/protobuf/compiler/rust/oneof.h
x64-windows/include/google/protobuf/compiler/rust/relative_path.h
x64-windows/include/google/protobuf/compiler/rust/rust_field_type.h
x64-windows/include/google/protobuf/compiler/rust/rust_keywords.h
x64-windows/include/google/protobuf/compiler/rust/upb_helpers.h
x64-windows/include/google/protobuf/compiler/scc.h
x64-windows/include/google/protobuf/compiler/subprocess.h
x64-windows/include/google/protobuf/compiler/versions.h
x64-windows/include/google/protobuf/compiler/zip_writer.h
x64-windows/include/google/protobuf/cpp_edition_defaults.h
x64-windows/include/google/protobuf/cpp_features.pb.h
x64-windows/include/google/protobuf/cpp_features.proto
x64-windows/include/google/protobuf/descriptor.h
x64-windows/include/google/protobuf/descriptor.pb.h
x64-windows/include/google/protobuf/descriptor.proto
x64-windows/include/google/protobuf/descriptor_database.h
x64-windows/include/google/protobuf/descriptor_legacy.h
x64-windows/include/google/protobuf/descriptor_lite.h
x64-windows/include/google/protobuf/descriptor_visitor.h
x64-windows/include/google/protobuf/duration.pb.h
x64-windows/include/google/protobuf/duration.proto
x64-windows/include/google/protobuf/dynamic_message.h
x64-windows/include/google/protobuf/empty.pb.h
x64-windows/include/google/protobuf/empty.proto
x64-windows/include/google/protobuf/endian.h
x64-windows/include/google/protobuf/explicitly_constructed.h
x64-windows/include/google/protobuf/extension_set.h
x64-windows/include/google/protobuf/extension_set_inl.h
x64-windows/include/google/protobuf/feature_resolver.h
x64-windows/include/google/protobuf/field_access_listener.h
x64-windows/include/google/protobuf/field_mask.pb.h
x64-windows/include/google/protobuf/field_mask.proto
x64-windows/include/google/protobuf/generated_enum_reflection.h
x64-windows/include/google/protobuf/generated_enum_util.h
x64-windows/include/google/protobuf/generated_message_bases.h
x64-windows/include/google/protobuf/generated_message_reflection.h
x64-windows/include/google/protobuf/generated_message_tctable_decl.h
x64-windows/include/google/protobuf/generated_message_tctable_gen.h
x64-windows/include/google/protobuf/generated_message_tctable_impl.h
x64-windows/include/google/protobuf/generated_message_util.h
x64-windows/include/google/protobuf/has_bits.h
x64-windows/include/google/protobuf/implicit_weak_message.h
x64-windows/include/google/protobuf/inlined_string_field.h
x64-windows/include/google/protobuf/internal_visibility.h
x64-windows/include/google/protobuf/io/
x64-windows/include/google/protobuf/io/coded_stream.h
x64-windows/include/google/protobuf/io/gzip_stream.h
x64-windows/include/google/protobuf/io/io_win32.h
x64-windows/include/google/protobuf/io/printer.h
x64-windows/include/google/protobuf/io/strtod.h
x64-windows/include/google/protobuf/io/tokenizer.h
x64-windows/include/google/protobuf/io/zero_copy_sink.h
x64-windows/include/google/protobuf/io/zero_copy_stream.h
x64-windows/include/google/protobuf/io/zero_copy_stream_impl.h
x64-windows/include/google/protobuf/io/zero_copy_stream_impl_lite.h
x64-windows/include/google/protobuf/json/
x64-windows/include/google/protobuf/json/internal/
x64-windows/include/google/protobuf/json/internal/descriptor_traits.h
x64-windows/include/google/protobuf/json/internal/lexer.h
x64-windows/include/google/protobuf/json/internal/message_path.h
x64-windows/include/google/protobuf/json/internal/parser.h
x64-windows/include/google/protobuf/json/internal/parser_traits.h
x64-windows/include/google/protobuf/json/internal/unparser.h
x64-windows/include/google/protobuf/json/internal/unparser_traits.h
x64-windows/include/google/protobuf/json/internal/untyped_message.h
x64-windows/include/google/protobuf/json/internal/writer.h
x64-windows/include/google/protobuf/json/internal/zero_copy_buffered_stream.h
x64-windows/include/google/protobuf/json/json.h
x64-windows/include/google/protobuf/map.h
x64-windows/include/google/protobuf/map_entry.h
x64-windows/include/google/protobuf/map_field.h
x64-windows/include/google/protobuf/map_field_inl.h
x64-windows/include/google/protobuf/map_field_lite.h
x64-windows/include/google/protobuf/map_type_handler.h
x64-windows/include/google/protobuf/message.h
x64-windows/include/google/protobuf/message_lite.h
x64-windows/include/google/protobuf/metadata.h
x64-windows/include/google/protobuf/metadata_lite.h
x64-windows/include/google/protobuf/parse_context.h
x64-windows/include/google/protobuf/port.h
x64-windows/include/google/protobuf/port_def.inc
x64-windows/include/google/protobuf/port_undef.inc
x64-windows/include/google/protobuf/raw_ptr.h
x64-windows/include/google/protobuf/reflection.h
x64-windows/include/google/protobuf/reflection_internal.h
x64-windows/include/google/protobuf/reflection_mode.h
x64-windows/include/google/protobuf/reflection_ops.h
x64-windows/include/google/protobuf/reflection_visit_field_info.h
x64-windows/include/google/protobuf/reflection_visit_fields.h
x64-windows/include/google/protobuf/repeated_field.h
x64-windows/include/google/protobuf/repeated_ptr_field.h
x64-windows/include/google/protobuf/runtime_version.h
x64-windows/include/google/protobuf/serial_arena.h
x64-windows/include/google/protobuf/service.h
x64-windows/include/google/protobuf/source_context.pb.h
x64-windows/include/google/protobuf/source_context.proto
x64-windows/include/google/protobuf/string_block.h
x64-windows/include/google/protobuf/struct.pb.h
x64-windows/include/google/protobuf/struct.proto
x64-windows/include/google/protobuf/stubs/
x64-windows/include/google/protobuf/stubs/callback.h
x64-windows/include/google/protobuf/stubs/common.h
x64-windows/include/google/protobuf/stubs/platform_macros.h
x64-windows/include/google/protobuf/stubs/port.h
x64-windows/include/google/protobuf/stubs/status_macros.h
x64-windows/include/google/protobuf/testing/
x64-windows/include/google/protobuf/testing/file.h
x64-windows/include/google/protobuf/text_format.h
x64-windows/include/google/protobuf/thread_safe_arena.h
x64-windows/include/google/protobuf/timestamp.pb.h
x64-windows/include/google/protobuf/timestamp.proto
x64-windows/include/google/protobuf/type.pb.h
x64-windows/include/google/protobuf/type.proto
x64-windows/include/google/protobuf/unknown_field_set.h
x64-windows/include/google/protobuf/util/
x64-windows/include/google/protobuf/util/delimited_message_util.h
x64-windows/include/google/protobuf/util/field_comparator.h
x64-windows/include/google/protobuf/util/field_mask_util.h
x64-windows/include/google/protobuf/util/json_util.h
x64-windows/include/google/protobuf/util/message_differencer.h
x64-windows/include/google/protobuf/util/time_util.h
x64-windows/include/google/protobuf/util/type_resolver.h
x64-windows/include/google/protobuf/util/type_resolver_util.h
x64-windows/include/google/protobuf/varint_shuffle.h
x64-windows/include/google/protobuf/wire_format.h
x64-windows/include/google/protobuf/wire_format_lite.h
x64-windows/include/google/protobuf/wrappers.pb.h
x64-windows/include/google/protobuf/wrappers.proto
x64-windows/include/upb/
x64-windows/include/upb/port/
x64-windows/include/upb/port/atomic.h
x64-windows/include/upb/port/def.inc
x64-windows/include/upb/port/undef.inc
x64-windows/include/upb/port/vsnprintf_compat.h
x64-windows/include/upb_generator/
x64-windows/include/upb_generator/common/
x64-windows/include/upb_generator/common/names.h
x64-windows/include/upb_generator/minitable/
x64-windows/include/upb_generator/minitable/names.h
x64-windows/include/upb_generator/minitable/names_internal.h
x64-windows/lib/
x64-windows/lib/libprotobuf-lite.lib
x64-windows/lib/libprotobuf.lib
x64-windows/lib/libprotoc.lib
x64-windows/lib/pkgconfig/
x64-windows/lib/pkgconfig/protobuf-lite.pc
x64-windows/lib/pkgconfig/protobuf.pc
x64-windows/share/
x64-windows/share/protobuf/
x64-windows/share/protobuf/copyright
x64-windows/share/protobuf/protobuf-config-version.cmake
x64-windows/share/protobuf/protobuf-config.cmake
x64-windows/share/protobuf/protobuf-generate.cmake
x64-windows/share/protobuf/protobuf-module.cmake
x64-windows/share/protobuf/protobuf-options.cmake
x64-windows/share/protobuf/protobuf-targets-debug.cmake
x64-windows/share/protobuf/protobuf-targets-release.cmake
x64-windows/share/protobuf/protobuf-targets.cmake
x64-windows/share/protobuf/vcpkg-cmake-wrapper.cmake
x64-windows/share/protobuf/vcpkg.spdx.json
x64-windows/share/protobuf/vcpkg_abi_info.txt
x64-windows/tools/
x64-windows/tools/protobuf/
x64-windows/tools/protobuf/abseil_dll.dll
x64-windows/tools/protobuf/libprotobuf.dll
x64-windows/tools/protobuf/libprotoc.dll
x64-windows/tools/protobuf/protoc.exe
