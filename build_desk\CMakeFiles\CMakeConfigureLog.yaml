
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake:204 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.26058 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/Program Files/LLVM/bin/clang.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is Clang, found in:
        F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/CMakeFiles/3.26.4/CompilerIdC/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:1153 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:221 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting C compiler /showIncludes prefix:
        
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:1153 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:221 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting C compiler /showIncludes prefix:
        
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Program Files/LLVM/bin/clang++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is Clang, found in:
        F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/CMakeFiles/3.26.4/CompilerIdCXX/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:1153 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:221 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:1153 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:221 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/CMakeFiles/CMakeScratch/TryCompile-mudfu6"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/CMakeFiles/CMakeScratch/TryCompile-mudfu6"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd"
      CMAKE_EXE_LINKER_FLAGS: ""
      VCPKG_INSTALLED_DIR: "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/vcpkg_installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/dev/vcpkg"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/CMakeFiles/CMakeScratch/TryCompile-mudfu6
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_17fad && [1/2] C:\\PROGRA~1\\LLVM\\bin\\clang.exe   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_17fad.dir/CMakeCCompilerABI.c.obj -MF CMakeFiles\\cmTC_17fad.dir\\CMakeCCompilerABI.c.obj.d -o CMakeFiles/cmTC_17fad.dir/CMakeCCompilerABI.c.obj -c "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeCCompilerABI.c"
        [2/2] cmd.exe /C "cd . && C:\\PROGRA~1\\LLVM\\bin\\clang.exe -fuse-ld=lld-link -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console CMakeFiles/cmTC_17fad.dir/CMakeCCompilerABI.c.obj -o cmTC_17fad.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_17fad.lib -Xlinker /pdb:cmTC_17fad.pdb -Xlinker /version:0.0     && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/CMakeFiles/CMakeScratch/TryCompile-e1arky"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/CMakeFiles/CMakeScratch/TryCompile-e1arky"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd"
      CMAKE_EXE_LINKER_FLAGS: ""
      VCPKG_INSTALLED_DIR: "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/vcpkg_installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/dev/vcpkg"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/CMakeFiles/CMakeScratch/TryCompile-e1arky
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_d041d && [1/2] C:\\PROGRA~1\\LLVM\\bin\\clang++.exe   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_d041d.dir/CMakeCXXCompilerABI.cpp.obj -MF CMakeFiles\\cmTC_d041d.dir\\CMakeCXXCompilerABI.cpp.obj.d -o CMakeFiles/cmTC_d041d.dir/CMakeCXXCompilerABI.cpp.obj -c "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeCXXCompilerABI.cpp"
        [2/2] cmd.exe /C "cd . && C:\\PROGRA~1\\LLVM\\bin\\clang++.exe -fuse-ld=lld-link -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console CMakeFiles/cmTC_d041d.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_d041d.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_d041d.lib -Xlinker /pdb:cmTC_d041d.pdb -Xlinker /version:0.0     && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/FeatureTesting.cmake:34 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/FeatureTesting.cmake:83 (_record_compiler_features)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake:121 (_record_compiler_features_cxx)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompileFeatures.cmake:70 (cmake_record_cxx_compile_features)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:70 (CMAKE_DETERMINE_COMPILE_FEATURES)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compile features"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/CMakeFiles/CMakeScratch/TryCompile-pjcj0v"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/CMakeFiles/CMakeScratch/TryCompile-pjcj0v"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd"
      CMAKE_EXE_LINKER_FLAGS: ""
      VCPKG_INSTALLED_DIR: "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/vcpkg_installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/dev/vcpkg"
    buildResult:
      variable: "CMAKE_CXX_FEATURE_TEST"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/CMakeFiles/CMakeScratch/TryCompile-pjcj0v
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_4623c && [1/2] C:\\PROGRA~1\\LLVM\\bin\\clang++.exe   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd   -std=c++14 -MD -MT CMakeFiles/cmTC_4623c.dir/feature_tests.cxx.obj -MF CMakeFiles\\cmTC_4623c.dir\\feature_tests.cxx.obj.d -o CMakeFiles/cmTC_4623c.dir/feature_tests.cxx.obj -c F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/CMakeFiles/CMakeScratch/TryCompile-pjcj0v/feature_tests.cxx
        [2/2] cmd.exe /C "cd . && C:\\PROGRA~1\\LLVM\\bin\\clang++.exe -fuse-ld=lld-link -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console CMakeFiles/cmTC_4623c.dir/feature_tests.cxx.obj -o cmTC_4623c.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_4623c.lib -Xlinker /pdb:cmTC_4623c.pdb -Xlinker /version:0.0   -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/FeatureTesting.cmake:34 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/FeatureTesting.cmake:83 (_record_compiler_features)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake:129 (_record_compiler_features_cxx)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompileFeatures.cmake:70 (cmake_record_cxx_compile_features)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:70 (CMAKE_DETERMINE_COMPILE_FEATURES)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compile features"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/CMakeFiles/CMakeScratch/TryCompile-029cqf"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/CMakeFiles/CMakeScratch/TryCompile-029cqf"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd"
      CMAKE_EXE_LINKER_FLAGS: ""
      VCPKG_INSTALLED_DIR: "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/vcpkg_installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/dev/vcpkg"
    buildResult:
      variable: "CMAKE_CXX_FEATURE_TEST"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/CMakeFiles/CMakeScratch/TryCompile-029cqf
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_e1baa && [1/2] C:\\PROGRA~1\\LLVM\\bin\\clang++.exe   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd   -std=c++14 -MD -MT CMakeFiles/cmTC_e1baa.dir/feature_tests.cxx.obj -MF CMakeFiles\\cmTC_e1baa.dir\\feature_tests.cxx.obj.d -o CMakeFiles/cmTC_e1baa.dir/feature_tests.cxx.obj -c F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250711/build_desk/CMakeFiles/CMakeScratch/TryCompile-029cqf/feature_tests.cxx
        [2/2] cmd.exe /C "cd . && C:\\PROGRA~1\\LLVM\\bin\\clang++.exe -fuse-ld=lld-link -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console CMakeFiles/cmTC_e1baa.dir/feature_tests.cxx.obj -o cmTC_e1baa.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_e1baa.lib -Xlinker /pdb:cmTC_e1baa.pdb -Xlinker /version:0.0   -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        
      exitCode: 0
...
