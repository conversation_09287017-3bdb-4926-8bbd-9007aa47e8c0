x64-windows/
x64-windows/bin/
x64-windows/bin/fontconfig-1.dll
x64-windows/bin/fontconfig-1.pdb
x64-windows/debug/
x64-windows/debug/bin/
x64-windows/debug/bin/fontconfig-1.dll
x64-windows/debug/bin/fontconfig-1.pdb
x64-windows/debug/lib/
x64-windows/debug/lib/fontconfig.lib
x64-windows/debug/lib/pkgconfig/
x64-windows/debug/lib/pkgconfig/fontconfig.pc
x64-windows/etc/
x64-windows/etc/fonts/
x64-windows/etc/fonts/conf.d/
x64-windows/etc/fonts/conf.d/10-hinting-slight.conf
x64-windows/etc/fonts/conf.d/10-scale-bitmap-fonts.conf
x64-windows/etc/fonts/conf.d/10-sub-pixel-none.conf
x64-windows/etc/fonts/conf.d/10-yes-antialias.conf
x64-windows/etc/fonts/conf.d/11-lcdfilter-default.conf
x64-windows/etc/fonts/conf.d/20-unhint-small-vera.conf
x64-windows/etc/fonts/conf.d/30-metric-aliases.conf
x64-windows/etc/fonts/conf.d/40-nonlatin.conf
x64-windows/etc/fonts/conf.d/45-generic.conf
x64-windows/etc/fonts/conf.d/45-latin.conf
x64-windows/etc/fonts/conf.d/48-spacing.conf
x64-windows/etc/fonts/conf.d/49-sansserif.conf
x64-windows/etc/fonts/conf.d/50-user.conf
x64-windows/etc/fonts/conf.d/51-local.conf
x64-windows/etc/fonts/conf.d/60-generic.conf
x64-windows/etc/fonts/conf.d/60-latin.conf
x64-windows/etc/fonts/conf.d/65-fonts-persian.conf
x64-windows/etc/fonts/conf.d/65-nonlatin.conf
x64-windows/etc/fonts/conf.d/69-unifont.conf
x64-windows/etc/fonts/conf.d/80-delicious.conf
x64-windows/etc/fonts/conf.d/90-synthetic.conf
x64-windows/etc/fonts/conf.d/README
x64-windows/etc/fonts/fonts.conf
x64-windows/include/
x64-windows/include/fontconfig/
x64-windows/include/fontconfig/fcfreetype.h
x64-windows/include/fontconfig/fcprivate.h
x64-windows/include/fontconfig/fontconfig.h
x64-windows/lib/
x64-windows/lib/fontconfig.lib
x64-windows/lib/pkgconfig/
x64-windows/lib/pkgconfig/fontconfig.pc
x64-windows/share/
x64-windows/share/fontconfig/
x64-windows/share/fontconfig/conf.avail/
x64-windows/share/fontconfig/conf.avail/05-reset-dirs-sample.conf
x64-windows/share/fontconfig/conf.avail/09-autohint-if-no-hinting.conf
x64-windows/share/fontconfig/conf.avail/10-autohint.conf
x64-windows/share/fontconfig/conf.avail/10-hinting-full.conf
x64-windows/share/fontconfig/conf.avail/10-hinting-medium.conf
x64-windows/share/fontconfig/conf.avail/10-hinting-none.conf
x64-windows/share/fontconfig/conf.avail/10-hinting-slight.conf
x64-windows/share/fontconfig/conf.avail/10-no-antialias.conf
x64-windows/share/fontconfig/conf.avail/10-scale-bitmap-fonts.conf
x64-windows/share/fontconfig/conf.avail/10-sub-pixel-bgr.conf
x64-windows/share/fontconfig/conf.avail/10-sub-pixel-none.conf
x64-windows/share/fontconfig/conf.avail/10-sub-pixel-rgb.conf
x64-windows/share/fontconfig/conf.avail/10-sub-pixel-vbgr.conf
x64-windows/share/fontconfig/conf.avail/10-sub-pixel-vrgb.conf
x64-windows/share/fontconfig/conf.avail/10-unhinted.conf
x64-windows/share/fontconfig/conf.avail/10-yes-antialias.conf
x64-windows/share/fontconfig/conf.avail/11-lcdfilter-default.conf
x64-windows/share/fontconfig/conf.avail/11-lcdfilter-legacy.conf
x64-windows/share/fontconfig/conf.avail/11-lcdfilter-light.conf
x64-windows/share/fontconfig/conf.avail/11-lcdfilter-none.conf
x64-windows/share/fontconfig/conf.avail/20-unhint-small-vera.conf
x64-windows/share/fontconfig/conf.avail/25-unhint-nonlatin.conf
x64-windows/share/fontconfig/conf.avail/30-metric-aliases.conf
x64-windows/share/fontconfig/conf.avail/35-lang-normalize.conf
x64-windows/share/fontconfig/conf.avail/40-nonlatin.conf
x64-windows/share/fontconfig/conf.avail/45-generic.conf
x64-windows/share/fontconfig/conf.avail/45-latin.conf
x64-windows/share/fontconfig/conf.avail/48-spacing.conf
x64-windows/share/fontconfig/conf.avail/49-sansserif.conf
x64-windows/share/fontconfig/conf.avail/50-user.conf
x64-windows/share/fontconfig/conf.avail/51-local.conf
x64-windows/share/fontconfig/conf.avail/60-generic.conf
x64-windows/share/fontconfig/conf.avail/60-latin.conf
x64-windows/share/fontconfig/conf.avail/65-fonts-persian.conf
x64-windows/share/fontconfig/conf.avail/65-khmer.conf
x64-windows/share/fontconfig/conf.avail/65-nonlatin.conf
x64-windows/share/fontconfig/conf.avail/69-unifont.conf
x64-windows/share/fontconfig/conf.avail/70-no-bitmaps.conf
x64-windows/share/fontconfig/conf.avail/70-yes-bitmaps.conf
x64-windows/share/fontconfig/conf.avail/80-delicious.conf
x64-windows/share/fontconfig/conf.avail/90-synthetic.conf
x64-windows/share/fontconfig/copyright
x64-windows/share/fontconfig/usage
x64-windows/share/fontconfig/vcpkg-cmake-wrapper.cmake
x64-windows/share/fontconfig/vcpkg.spdx.json
x64-windows/share/fontconfig/vcpkg_abi_info.txt
x64-windows/share/gettext/
x64-windows/share/gettext/its/
x64-windows/share/gettext/its/fontconfig.its
x64-windows/share/gettext/its/fontconfig.loc
x64-windows/share/xml/
x64-windows/share/xml/fontconfig/
x64-windows/share/xml/fontconfig/fonts.dtd
