/* OSG Interface Compatibility Test Framework Implementation
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#include "OSGInterfaceTestFramework.h"

#include <osg/GraphicsContext>
#include <osgViewer/GraphicsWindow>
#include <iostream>
#include <fstream>
#include <sstream>

#ifdef __EMSCRIPTEN__
#include <emscripten.h>
#include <emscripten/html5.h>
#include <GLES3/gl3.h>
#else
#include <osg/GL>
#include <osg/GLExtensions>
#endif

namespace OSGCompatibilityTest
{

    // TestSuite Implementation
    std::vector<TestSuite::TestResult_Detail> TestSuite::runAllTests(osg::RenderInfo &renderInfo)
    {
        std::vector<TestResult_Detail> results;

        std::cout << "Running test suite: " << _name << std::endl;

        for (auto &test : _tests)
        {
            TestResult_Detail detail;
            detail.testName = test->getName();

            PerformanceMeasure perf;
            perf.start();

            try
            {
                if (test->setup())
                {
                    detail.result = test->runTest(renderInfo);
                    test->cleanup();
                }
                else
                {
                    detail.result = TestResult::FAIL;
                    detail.errorMessage = "Setup failed";
                }
            }
            catch (const std::exception &e)
            {
                detail.result = TestResult::FAIL;
                detail.errorMessage = e.what();
            }
            catch (...)
            {
                detail.result = TestResult::FAIL;
                detail.errorMessage = "Unknown exception";
            }

            perf.stop();
            detail.executionTimeMs = perf.getElapsedMs();

            // 输出测试结果
            const char *resultStr = "UNKNOWN";
            switch (detail.result)
            {
            case TestResult::PASS:
                resultStr = "PASS";
                break;
            case TestResult::FAIL:
                resultStr = "FAIL";
                break;
            case TestResult::SKIP:
                resultStr = "SKIP";
                break;
            case TestResult::NOT_SUPPORTED:
                resultStr = "NOT_SUPPORTED";
                break;
            }

            std::cout << "  " << detail.testName << ": " << resultStr;
            if (!detail.errorMessage.empty())
            {
                std::cout << " (" << detail.errorMessage << ")";
            }
            std::cout << " [" << detail.executionTimeMs << "ms]" << std::endl;

            results.push_back(detail);
        }

        return results;
    }

    // OSGInterfaceTestFramework Implementation
    OSGInterfaceTestFramework::OSGInterfaceTestFramework()
    {
    }

    OSGInterfaceTestFramework::~OSGInterfaceTestFramework()
    {
        cleanupGraphicsContext();
    }

    bool OSGInterfaceTestFramework::initialize()
    {
        if (_initialized)
        {
            return true;
        }

        std::cout << "Initializing OSG Interface Test Framework..." << std::endl;
        std::cout << "Platform: " << PlatformInfo::getPlatformName() << std::endl;

#ifdef __EMSCRIPTEN__
        // WebAssembly环境下的安全初始化
        std::cout << "Running in WebAssembly/Emscripten environment" << std::endl;

        // 跳过复杂的图形上下文创建，使用简化版本
        if (!setupGraphicsContextSimple())
        {
            std::cerr << "Failed to setup simple graphics context" << std::endl;
            return false;
        }
#else
        if (!setupGraphicsContext())
        {
            std::cerr << "Failed to setup graphics context" << std::endl;
            return false;
        }
#endif

        GLStateChecker::logGLInfo();

        _initialized = true;
        return true;
    }

    bool OSGInterfaceTestFramework::setupGraphicsContext()
    {
        try
        {
            _viewer = new osgViewer::Viewer();

#ifdef __EMSCRIPTEN__
            // WebAssembly环境下的图形上下文设置
            osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits();
            traits->x = 0;
            traits->y = 0;
            traits->width = 800;
            traits->height = 600;
            traits->windowDecoration = false;
            traits->doubleBuffer = true;
            traits->sharedContext = nullptr;

            _gc = osg::GraphicsContext::createGraphicsContext(traits.get());
#else
            // 桌面环境下的图形上下文设置 - 使用pbuffer避免窗口显示
            osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits();
            traits->x = 0;
            traits->y = 0;
            traits->width = 800;
            traits->height = 600;
            traits->pbuffer = true;       // 使用pbuffer
            traits->doubleBuffer = false; // pbuffer不需要双缓冲
            traits->sharedContext = nullptr;

            _gc = osg::GraphicsContext::createGraphicsContext(traits.get());
#endif

            if (!_gc || !_gc->valid())
            {
                std::cerr << "Failed to create graphics context" << std::endl;
                return false;
            }

            // 设置相机
            osg::Camera *camera = _viewer->getCamera();
            camera->setGraphicsContext(_gc.get());
            camera->setViewport(new osg::Viewport(0, 0, 800, 600));

            // 实现图形上下文
            _gc->realize();

            // 确保上下文当前
            if (!_gc->makeCurrent())
            {
                std::cerr << "Failed to make graphics context current" << std::endl;
                return false;
            }

            return true;
        }
        catch (const std::exception &e)
        {
            std::cerr << "Exception in setupGraphicsContext: " << e.what() << std::endl;
            return false;
        }
    }

    bool OSGInterfaceTestFramework::setupGraphicsContextSimple()
    {
        try
        {
            // WebAssembly简化版本 - 避免复杂的OSG初始化
            std::cout << "Setting up simplified graphics context for WebAssembly..." << std::endl;

            // 不创建实际的图形上下文，只设置基本状态
            _viewer = nullptr; // 避免OSG viewer的复杂初始化
            _gc = nullptr;     // 避免图形上下文创建

            std::cout << "Simplified graphics context setup completed" << std::endl;
            return true;
        }
        catch (const std::exception &e)
        {
            std::cerr << "Exception in setupGraphicsContextSimple: " << e.what() << std::endl;
            return false;
        }
    }

    void OSGInterfaceTestFramework::cleanupGraphicsContext()
    {
        if (_gc.valid())
        {
            _gc->close();
            _gc = nullptr;
        }
        _viewer = nullptr;
    }

    void OSGInterfaceTestFramework::addTestSuite(std::unique_ptr<TestSuite> suite)
    {
        _testSuites.push_back(std::move(suite));
    }

    void OSGInterfaceTestFramework::runAllTests()
    {
        if (!_initialized)
        {
            std::cerr << "Framework not initialized" << std::endl;
            return;
        }

        _allResults.clear();

        // 创建RenderInfo
        osg::ref_ptr<osg::State> state = _gc->getState();
        osg::RenderInfo renderInfo;
        renderInfo.setState(state.get());
        renderInfo.setView(_viewer.get());

        std::cout << "\n=== Running All OSG Interface Compatibility Tests ===" << std::endl;

        for (auto &suite : _testSuites)
        {
            auto results = suite->runAllTests(renderInfo);
            _allResults.insert(_allResults.end(), results.begin(), results.end());
        }

        // 输出总结
        auto stats = getStatistics();
        std::cout << "\n=== Test Summary ===" << std::endl;
        std::cout << "Total Tests: " << stats.totalTests << std::endl;
        std::cout << "Passed: " << stats.passedTests << std::endl;
        std::cout << "Failed: " << stats.failedTests << std::endl;
        std::cout << "Skipped: " << stats.skippedTests << std::endl;
        std::cout << "Not Supported: " << stats.notSupportedTests << std::endl;
        std::cout << "Total Execution Time: " << stats.totalExecutionTimeMs << "ms" << std::endl;
    }

    void OSGInterfaceTestFramework::runTestSuite(const std::string &suiteName)
    {
        // 实现运行指定测试套件的逻辑
        for (auto &suite : _testSuites)
        {
            if (suite->getName() == suiteName)
            {
                osg::ref_ptr<osg::State> state = _gc->getState();
                osg::RenderInfo renderInfo;
                renderInfo.setState(state.get());
                renderInfo.setView(_viewer.get());

                auto results = suite->runAllTests(renderInfo);
                _allResults.insert(_allResults.end(), results.begin(), results.end());
                return;
            }
        }
        std::cerr << "Test suite not found: " << suiteName << std::endl;
    }

    OSGInterfaceTestFramework::TestStatistics OSGInterfaceTestFramework::getStatistics() const
    {
        TestStatistics stats;

        for (const auto &result : _allResults)
        {
            stats.totalTests++;
            stats.totalExecutionTimeMs += result.executionTimeMs;

            switch (result.result)
            {
            case TestResult::PASS:
                stats.passedTests++;
                break;
            case TestResult::FAIL:
                stats.failedTests++;
                break;
            case TestResult::SKIP:
                stats.skippedTests++;
                break;
            case TestResult::NOT_SUPPORTED:
                stats.notSupportedTests++;
                break;
            }
        }

        return stats;
    }

    void OSGInterfaceTestFramework::generateReport(const std::string &filename)
    {
        std::ofstream file(filename);
        if (!file.is_open())
        {
            std::cerr << "Failed to create report file: " << filename << std::endl;
            return;
        }

        auto stats = getStatistics();

        file << "<!DOCTYPE html>\n<html>\n<head>\n";
        file << "<title>OSG Interface Compatibility Test Report</title>\n";
        file << "<style>\n";
        file << "body { font-family: Arial, sans-serif; margin: 20px; }\n";
        file << ".pass { color: green; }\n";
        file << ".fail { color: red; }\n";
        file << ".skip { color: orange; }\n";
        file << ".not-supported { color: gray; }\n";
        file << "table { border-collapse: collapse; width: 100%; }\n";
        file << "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n";
        file << "th { background-color: #f2f2f2; }\n";
        file << "</style>\n</head>\n<body>\n";

        file << "<h1>OSG Interface Compatibility Test Report</h1>\n";
        file << "<p>Platform: " << PlatformInfo::getPlatformName() << "</p>\n";
        file << "<p>OpenGL Version: " << PlatformInfo::getOpenGLVersion() << "</p>\n";
        file << "<p>Renderer: " << PlatformInfo::getRenderer() << "</p>\n";

        file << "<h2>Summary</h2>\n";
        file << "<ul>\n";
        file << "<li>Total Tests: " << stats.totalTests << "</li>\n";
        file << "<li class='pass'>Passed: " << stats.passedTests << "</li>\n";
        file << "<li class='fail'>Failed: " << stats.failedTests << "</li>\n";
        file << "<li class='skip'>Skipped: " << stats.skippedTests << "</li>\n";
        file << "<li class='not-supported'>Not Supported: " << stats.notSupportedTests << "</li>\n";
        file << "<li>Total Execution Time: " << stats.totalExecutionTimeMs << "ms</li>\n";
        file << "</ul>\n";

        file << "<h2>Detailed Results</h2>\n";
        file << "<table>\n";
        file << "<tr><th>Test Name</th><th>Result</th><th>Error Message</th><th>Execution Time (ms)</th></tr>\n";

        for (const auto &result : _allResults)
        {
            std::string cssClass;
            std::string resultStr;
            switch (result.result)
            {
            case TestResult::PASS:
                cssClass = "pass";
                resultStr = "PASS";
                break;
            case TestResult::FAIL:
                cssClass = "fail";
                resultStr = "FAIL";
                break;
            case TestResult::SKIP:
                cssClass = "skip";
                resultStr = "SKIP";
                break;
            case TestResult::NOT_SUPPORTED:
                cssClass = "not-supported";
                resultStr = "NOT_SUPPORTED";
                break;
            }

            file << "<tr class='" << cssClass << "'>";
            file << "<td>" << result.testName << "</td>";
            file << "<td>" << resultStr << "</td>";
            file << "<td>" << result.errorMessage << "</td>";
            file << "<td>" << result.executionTimeMs << "</td>";
            file << "</tr>\n";
        }

        file << "</table>\n";
        file << "</body>\n</html>\n";

        file.close();
        std::cout << "Test report generated: " << filename << std::endl;
    }

    // PlatformInfo Implementation
    bool PlatformInfo::isWebAssembly()
    {
#ifdef __EMSCRIPTEN__
        return true;
#else
        return false;
#endif
    }

    bool PlatformInfo::isDesktop()
    {
        return !isWebAssembly();
    }

    std::string PlatformInfo::getPlatformName()
    {
#ifdef __EMSCRIPTEN__
        return "WebAssembly/Emscripten";
#else
        return "Desktop";
#endif
    }

    std::string PlatformInfo::getOpenGLVersion()
    {
        // 使用OSG的方式获取OpenGL版本信息
        return "OpenGL (via OSG)";
    }

    std::string PlatformInfo::getRenderer()
    {
        // 使用OSG的方式获取渲染器信息
        return "OSG Renderer";
    }

    // GLStateChecker Implementation
    bool GLStateChecker::checkGLError(const std::string &operation)
    {
        // 使用OSG的错误检查机制
        return true; // 暂时返回true，实际应该使用OSG的错误检查
    }

    void GLStateChecker::logGLInfo()
    {
        std::cout << "OpenGL Version: " << PlatformInfo::getOpenGLVersion() << std::endl;
        std::cout << "Renderer: " << PlatformInfo::getRenderer() << std::endl;
    }

    // PerformanceMeasure Implementation
    PerformanceMeasure::PerformanceMeasure()
    {
    }

    void PerformanceMeasure::start()
    {
        _startTime = std::chrono::high_resolution_clock::now();
        _running = true;
    }

    void PerformanceMeasure::stop()
    {
        _endTime = std::chrono::high_resolution_clock::now();
        _running = false;
    }

    double PerformanceMeasure::getElapsedMs() const
    {
        auto endTime = _running ? std::chrono::high_resolution_clock::now() : _endTime;
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - _startTime);
        return duration.count() / 1000.0;
    }

} // namespace OSGCompatibilityTest
