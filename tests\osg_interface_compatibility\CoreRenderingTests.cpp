/* Core OSG Rendering Interface Tests Implementation
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#include "CoreRenderingTests.h"
#include <osg/Texture2D>
#include <osg/Image>
#include <osg/Program>
#include <osg/Shader>

#ifdef __EMSCRIPTEN__
#include <GLES3/gl3.h>
#else
#include <GL/gl.h>
#endif

namespace OSGCompatibilityTest
{

    // BasicDrawableTest Implementation
    IMPLEMENT_OSG_TEST(BasicDrawableTest)
    {
        try
        {
            // 创建测试用的Drawable
            osg::ref_ptr<TestDrawable> drawable = new TestDrawable();

            // 执行绘制
            drawable->draw(renderInfo);

            // 检查结果
            OSG_TEST_ASSERT(drawable->getLastDrawSuccess(),
                            "Basic drawable draw failed: " + drawable->getLastError());

            OSG_TEST_ASSERT_GL_NO_ERROR("After basic drawable draw");

            return TestResult::PASS;
        }
        catch (const std::exception &e)
        {
            return TestResult::FAIL;
        }
    }

    // StateVBOTest Implementation
    IMPLEMENT_OSG_TEST(StateVBOTest)
    {
        try
        {
            osg::State *state = renderInfo.getState();

            // 创建测试几何体
            osg::ref_ptr<osg::Geometry> geom = createTestTriangle();

            // 测试VBO绑定
            osg::VertexBufferObject *vbo = geom->getVertexArray()->getVertexBufferObject();
            if (vbo)
            {
                // 绑定VBO (使用getGLBufferObject方法)
                state->bindVertexBufferObject(vbo->getGLBufferObject(renderInfo.getContextID()));
                OSG_TEST_ASSERT_GL_NO_ERROR("VBO bind");

                // 解绑VBO
                state->unbindVertexBufferObject();
                OSG_TEST_ASSERT_GL_NO_ERROR("VBO unbind");
            }

            return TestResult::PASS;
        }
        catch (const std::exception &e)
        {
            return TestResult::FAIL;
        }
    }

    // StateEBOTest Implementation
    IMPLEMENT_OSG_TEST(StateEBOTest)
    {
        try
        {
            osg::State *state = renderInfo.getState();

            // 创建测试几何体
            osg::ref_ptr<osg::Geometry> geom = createTestQuad();

            // 测试EBO绑定 - 使用DrawElementsUInt来获取EBO
            osg::DrawElementsUInt *drawElements = dynamic_cast<osg::DrawElementsUInt *>(geom->getPrimitiveSet(0));
            if (drawElements)
            {
                osg::ElementBufferObject *ebo = drawElements->getElementBufferObject();
                if (ebo)
                {
                    // 绑定EBO (使用getGLBufferObject方法)
                    state->bindElementBufferObject(ebo->getGLBufferObject(renderInfo.getContextID()));
                    OSG_TEST_ASSERT_GL_NO_ERROR("EBO bind");

                    // 解绑EBO
                    state->unbindElementBufferObject();
                    OSG_TEST_ASSERT_GL_NO_ERROR("EBO unbind");
                }
            }

            return TestResult::PASS;
        }
        catch (const std::exception &e)
        {
            return TestResult::FAIL;
        }
    }

    // GLErrorCheckTest Implementation
    IMPLEMENT_OSG_TEST(GLErrorCheckTest)
    {
        try
        {
            osg::State *state = renderInfo.getState();

            // 测试错误检查功能
            bool originalCheck = state->getCheckForGLErrors() != osg::State::NEVER_CHECK_GL_ERRORS;

            // 启用错误检查
            state->setCheckForGLErrors(osg::State::ONCE_PER_ATTRIBUTE);

            // 执行一个正常的OpenGL操作
            glGetError(); // 清除之前的错误

            // 检查错误检查机制
            bool result = state->checkGLErrors("test operation");

            // 恢复原始设置
            state->setCheckForGLErrors(originalCheck ? osg::State::ONCE_PER_ATTRIBUTE : osg::State::NEVER_CHECK_GL_ERRORS);

            return TestResult::PASS;
        }
        catch (const std::exception &e)
        {
            return TestResult::FAIL;
        }
    }

    // MatrixApplicationTest Implementation
    IMPLEMENT_OSG_TEST(MatrixApplicationTest)
    {
        try
        {
            osg::State *state = renderInfo.getState();

            // 创建测试矩阵
            osg::ref_ptr<osg::RefMatrix> projection = new osg::RefMatrix();
            projection->makePerspective(45.0, 1.0, 0.1, 100.0);

            osg::ref_ptr<osg::RefMatrix> modelview = new osg::RefMatrix();
            modelview->makeLookAt(osg::Vec3(0, 0, 5), osg::Vec3(0, 0, 0), osg::Vec3(0, 1, 0));

            // 应用矩阵
            state->applyProjectionMatrix(projection.get());
            OSG_TEST_ASSERT_GL_NO_ERROR("Apply projection matrix");

            state->applyModelViewMatrix(modelview.get());
            OSG_TEST_ASSERT_GL_NO_ERROR("Apply modelview matrix");

            return TestResult::PASS;
        }
        catch (const std::exception &e)
        {
            return TestResult::FAIL;
        }
    }

    // StateSetApplicationTest Implementation
    IMPLEMENT_OSG_TEST(StateSetApplicationTest)
    {
        try
        {
            osg::State *state = renderInfo.getState();

            // 创建测试StateSet
            osg::ref_ptr<osg::StateSet> stateSet = new osg::StateSet();
            stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);
            stateSet->setMode(GL_BLEND, osg::StateAttribute::OFF);

            // 应用StateSet
            state->apply(stateSet.get());
            OSG_TEST_ASSERT_GL_NO_ERROR("Apply StateSet");

            return TestResult::PASS;
        }
        catch (const std::exception &e)
        {
            return TestResult::FAIL;
        }
    }

    // GeometryDrawTest Implementation
    IMPLEMENT_OSG_TEST(GeometryDrawTest)
    {
        try
        {
            // 创建测试几何体
            osg::ref_ptr<osg::Geometry> geom = createTestTriangle();

            // 执行绘制
            geom->drawImplementation(renderInfo);
            OSG_TEST_ASSERT_GL_NO_ERROR("Geometry draw");

            return TestResult::PASS;
        }
        catch (const std::exception &e)
        {
            return TestResult::FAIL;
        }
    }

    // TextureBindingTest Implementation
    IMPLEMENT_OSG_TEST(TextureBindingTest)
    {
        try
        {
            osg::State *state = renderInfo.getState();

            // 创建测试纹理
            osg::ref_ptr<osg::Texture2D> texture = createTestTexture();

            // 绑定纹理
            state->setActiveTextureUnit(0);
            texture->apply(*state);
            OSG_TEST_ASSERT_GL_NO_ERROR("Texture binding");

            return TestResult::PASS;
        }
        catch (const std::exception &e)
        {
            return TestResult::FAIL;
        }
    }

    // ShaderProgramTest Implementation
    IMPLEMENT_OSG_TEST(ShaderProgramTest)
    {
        try
        {
            osg::State *state = renderInfo.getState();

            // 创建测试着色器程序
            osg::ref_ptr<osg::Program> program = createTestShaderProgram();

            // 应用着色器程序
            state->applyAttribute(program.get());
            OSG_TEST_ASSERT_GL_NO_ERROR("Shader program application");

            return TestResult::PASS;
        }
        catch (const std::exception &e)
        {
            return TestResult::FAIL;
        }
    }

    // VAOOperationsTest Implementation
    IMPLEMENT_OSG_TEST(VAOOperationsTest)
    {
        // VAO在WebGL 1.0中不支持，在WebGL 2.0中支持
#ifdef __EMSCRIPTEN__
        // 检查WebGL版本
        const char *version = reinterpret_cast<const char *>(glGetString(GL_VERSION));
        if (version && strstr(version, "WebGL 1.0"))
        {
            return TestResult::NOT_SUPPORTED;
        }
#endif

        try
        {
            // 这里应该测试VAO操作，但OSG的VAO接口可能不直接暴露
            // 暂时返回跳过
            return TestResult::SKIP;
        }
        catch (const std::exception &e)
        {
            return TestResult::FAIL;
        }
    }

    // 创建核心渲染测试套件
    std::unique_ptr<TestSuite> createCoreRenderingTestSuite()
    {
        auto suite = std::make_unique<TestSuite>("Core Rendering Tests");

        suite->addTest(std::make_unique<BasicDrawableTest>());
        suite->addTest(std::make_unique<StateVBOTest>());
        suite->addTest(std::make_unique<StateEBOTest>());
        suite->addTest(std::make_unique<GLErrorCheckTest>());
        suite->addTest(std::make_unique<MatrixApplicationTest>());
        suite->addTest(std::make_unique<StateSetApplicationTest>());
        suite->addTest(std::make_unique<GeometryDrawTest>());
        suite->addTest(std::make_unique<TextureBindingTest>());
        suite->addTest(std::make_unique<ShaderProgramTest>());
        suite->addTest(std::make_unique<VAOOperationsTest>());

        return suite;
    }

    // TestDrawable Implementation
    TestDrawable::TestDrawable()
    {
        setupGeometry();
        setupTexture();
    }

    void TestDrawable::drawImplementation(osg::RenderInfo &renderInfo) const
    {
        try
        {
            _lastDrawSuccess = false;
            _lastError.clear();

            osg::State *state = renderInfo.getState();

            if (_useTexture && _texture.valid())
            {
                state->setActiveTextureUnit(0);
                _texture->apply(*state);
            }

            if (_geometry.valid())
            {
                _geometry->drawImplementation(renderInfo);
            }

            if (_useVBO)
            {
                state->unbindVertexBufferObject();
            }

            if (_useEBO)
            {
                state->unbindElementBufferObject();
            }

            _lastDrawSuccess = true;
        }
        catch (const std::exception &e)
        {
            _lastError = e.what();
            _lastDrawSuccess = false;
        }
    }

    void TestDrawable::setupGeometry()
    {
        _geometry = createTestTriangle();
    }

    void TestDrawable::setupTexture()
    {
        _texture = createTestTexture();
    }

    // 辅助函数实现
    osg::ref_ptr<osg::Geometry> createTestTriangle()
    {
        osg::ref_ptr<osg::Geometry> geom = new osg::Geometry();

        // 顶点数组
        osg::ref_ptr<osg::Vec3Array> vertices = new osg::Vec3Array();
        vertices->push_back(osg::Vec3(-1.0f, -1.0f, 0.0f));
        vertices->push_back(osg::Vec3(1.0f, -1.0f, 0.0f));
        vertices->push_back(osg::Vec3(0.0f, 1.0f, 0.0f));
        geom->setVertexArray(vertices.get());

        // 法线数组
        osg::ref_ptr<osg::Vec3Array> normals = new osg::Vec3Array();
        normals->push_back(osg::Vec3(0.0f, 0.0f, 1.0f));
        geom->setNormalArray(normals.get());
        geom->setNormalBinding(osg::Geometry::BIND_OVERALL);

        // 纹理坐标
        osg::ref_ptr<osg::Vec2Array> texCoords = new osg::Vec2Array();
        texCoords->push_back(osg::Vec2(0.0f, 0.0f));
        texCoords->push_back(osg::Vec2(1.0f, 0.0f));
        texCoords->push_back(osg::Vec2(0.5f, 1.0f));
        geom->setTexCoordArray(0, texCoords.get());

        // 图元集合
        geom->addPrimitiveSet(new osg::DrawArrays(osg::PrimitiveSet::TRIANGLES, 0, 3));

        return geom;
    }

    osg::ref_ptr<osg::Geometry> createTestQuad()
    {
        osg::ref_ptr<osg::Geometry> geom = new osg::Geometry();

        // 顶点数组
        osg::ref_ptr<osg::Vec3Array> vertices = new osg::Vec3Array();
        vertices->push_back(osg::Vec3(-1.0f, -1.0f, 0.0f));
        vertices->push_back(osg::Vec3(1.0f, -1.0f, 0.0f));
        vertices->push_back(osg::Vec3(1.0f, 1.0f, 0.0f));
        vertices->push_back(osg::Vec3(-1.0f, 1.0f, 0.0f));
        geom->setVertexArray(vertices.get());

        // 索引数组
        osg::ref_ptr<osg::DrawElementsUInt> indices = new osg::DrawElementsUInt(osg::PrimitiveSet::TRIANGLES, 0);
        indices->push_back(0);
        indices->push_back(1);
        indices->push_back(2);
        indices->push_back(0);
        indices->push_back(2);
        indices->push_back(3);
        geom->addPrimitiveSet(indices.get());

        return geom;
    }

    osg::ref_ptr<osg::Texture2D> createTestTexture(int width, int height)
    {
        // 创建简单的测试纹理
        osg::ref_ptr<osg::Image> image = new osg::Image();
        image->allocateImage(width, height, 1, GL_RGBA, GL_UNSIGNED_BYTE);

        // 填充棋盘格图案
        unsigned char *data = image->data();
        for (int y = 0; y < height; ++y)
        {
            for (int x = 0; x < width; ++x)
            {
                int index = (y * width + x) * 4;
                bool checker = ((x / 8) + (y / 8)) % 2;
                unsigned char color = checker ? 255 : 0;
                data[index + 0] = color; // R
                data[index + 1] = color; // G
                data[index + 2] = color; // B
                data[index + 3] = 255;   // A
            }
        }

        osg::ref_ptr<osg::Texture2D> texture = new osg::Texture2D();
        texture->setImage(image.get());
        texture->setWrap(osg::Texture::WRAP_S, osg::Texture::REPEAT);
        texture->setWrap(osg::Texture::WRAP_T, osg::Texture::REPEAT);
        texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR);
        texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);

        return texture;
    }

    osg::ref_ptr<osg::Program> createTestShaderProgram()
    {
        osg::ref_ptr<osg::Program> program = new osg::Program();

        // 顶点着色器
        const char *vertexShaderSource = R"(
            #ifdef GL_ES
            precision mediump float;
            #endif

            attribute vec3 osg_Vertex;
            attribute vec3 osg_Normal;
            attribute vec2 osg_MultiTexCoord0;

            uniform mat4 osg_ModelViewProjectionMatrix;

            varying vec2 texCoord;

            void main() {
                gl_Position = osg_ModelViewProjectionMatrix * vec4(osg_Vertex, 1.0);
                texCoord = osg_MultiTexCoord0;
            }
        )";

        // 片段着色器
        const char *fragmentShaderSource = R"(
            #ifdef GL_ES
            precision mediump float;
            #endif

            varying vec2 texCoord;

            void main() {
                gl_FragColor = vec4(texCoord, 0.5, 1.0);
            }
        )";

        osg::ref_ptr<osg::Shader> vertexShader = new osg::Shader(osg::Shader::VERTEX, vertexShaderSource);
        osg::ref_ptr<osg::Shader> fragmentShader = new osg::Shader(osg::Shader::FRAGMENT, fragmentShaderSource);

        program->addShader(vertexShader.get());
        program->addShader(fragmentShader.get());

        return program;
    }

} // namespace OSGCompatibilityTest
