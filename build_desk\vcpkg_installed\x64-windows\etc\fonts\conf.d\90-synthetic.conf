<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "urn:fontconfig:fonts.dtd">
<fontconfig>
<!-- 
 Artificial oblique for fonts without an italic or oblique version
 -->
 
	<match target="font">
		<!-- check to see if the font is roman -->
		<test name="slant">
			<const>roman</const>
		</test>
		<!-- check to see if the pattern requested non-roman -->
		<test target="pattern" name="slant" compare="not_eq">
			<const>roman</const>
		</test>
		<!-- multiply the matrix to slant the font -->
		<edit name="matrix" mode="assign">
			<times>
				<name>matrix</name>
				<matrix><double>1</double><double>0.2</double>
					<double>0</double><double>1</double>
				</matrix>
			</times>
		</edit>
		<!-- pretend the font is oblique now -->
		<edit name="slant" mode="assign">
			<const>oblique</const>
		</edit>
		<!-- and disable embedded bitmaps for artificial oblique -->
		<edit name="embeddedbitmap" mode="assign">
			<bool>false</bool>
		</edit>
	</match>

<!--
 Synthetic emboldening for fonts that do not have bold face available
 -->

	<match target="font">
		<!-- check to see if the weight in the font is less than medium which possibly need emboldening -->
		<test name="weight" compare="less_eq">
			<const>medium</const>
		</test>
		<!-- check to see if the pattern requests bold -->
		<test target="pattern" name="weight" compare="more_eq">
			<const>bold</const>
		</test>
		<!--
		  set the embolden flag
		  needed for applications using cairo, e.g. gucharmap, gedit, ...
		-->
		<edit name="embolden" mode="assign">
			<bool>true</bool>
		</edit>
		<!--
		 set weight to bold
		 needed for applications using Xft directly, e.g. Firefox, ...
		-->
		<edit name="weight" mode="assign">
			<const>bold</const>
		</edit>
	</match>
</fontconfig>
