cmake 3.30.1
create_package.patch 7829909725404e8328af4c5959c73b4fe1ec50481b11769d343d79925ce1b8e6
cxx-linkage-pkgconfig.patch cb3b6d8750603ad3e3dfcd3e8072ff36066e213065c007b6fea96b28360e674d
features core
fix-climits-include.patch ee87bca77c4057a47ed1692f7e6cf66babc2b391629d672cc682d03c2672a915
portfile.cmake 1450966caea88c9b70a837be464bb9cad0ccba6dca8c2be38031e3404bcfd184
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.2
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
vcpkg-cmake a0dc1d028307cd46ec7e45b561314f30e28ec952c29f9749d9aac9bdf5fed759
vcpkg-cmake-config 472514a630e790ecc84c4109c9d53da233d4ffd78017b825f9117c89e190f16e
vcpkg.json a4bc75532c10e7715b5f43c74b38407dfcd8f1e8e8b7d7be908ce9678513d57f
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_replace_string b450deb79207478b37119743e00808ebc42de0628e7b98c14ab24728bd5c78b8
