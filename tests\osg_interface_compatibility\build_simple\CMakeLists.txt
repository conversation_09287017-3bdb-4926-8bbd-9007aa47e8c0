# 简化的OSG接口兼容性测试 - WebAssembly版本
# 避免复杂的OSG依赖，专注于基础功能验证

cmake_minimum_required(VERSION 3.16)

project(OSGSimpleCompatibilityTest)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译器特定设置
if(EMSCRIPTEN)
    # WebAssembly构建设置
    add_executable(osg_simple_test ../main_wasm_simple.cpp)

    # Emscripten特定设置
    set_target_properties(osg_simple_test PROPERTIES
        SUFFIX ".html"
        LINK_FLAGS "-s USE_WEBGL2=1 -s FULL_ES3=1 -s WASM=1 -s ALLOW_MEMORY_GROWTH=1 -s NO_EXIT_RUNTIME=1 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap'] --shell-file ${CMAKE_CURRENT_SOURCE_DIR}/../shell_simple.html"
    )
    
    # 编译定义
    target_compile_definitions(osg_simple_test PRIVATE
        __EMSCRIPTEN__
        GL_GLEXT_PROTOTYPES
    )
    
    message(STATUS "Building simplified WebAssembly test")
    message(STATUS "Output: osg_simple_test.html")
    
else()
    # 桌面构建设置
    add_executable(osg_simple_test main_wasm_simple.cpp)
    
    # 设置输出目录
    set_target_properties(osg_simple_test PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    )
    
    message(STATUS "Building simplified desktop test")
    message(STATUS "Output: osg_simple_test")
endif()

# 生成编译数据库
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# 打印配置信息
message(STATUS "Simple OSG Compatibility Test Configuration:")
message(STATUS "  Platform: ${CMAKE_SYSTEM_NAME}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")

if(EMSCRIPTEN)
    message(STATUS "  Emscripten: YES")
    message(STATUS "  Target: WebAssembly")
else()
    message(STATUS "  Emscripten: NO")
    message(STATUS "  Target: Desktop")
endif()
