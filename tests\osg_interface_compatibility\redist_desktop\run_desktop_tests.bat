@echo off
echo ========================================
echo OSG接口兼容性测试 - 桌面版
echo ========================================

:: 设置网络代理环境变量
echo 配置网络代理...
set HTTP_PROXY=http://127.0.0.1:10809
set HTTPS_PROXY=http://127.0.0.1:10809
set http_proxy=http://127.0.0.1:10809
set https_proxy=http://127.0.0.1:10809

echo 代理配置完成:
echo   HTTP_PROXY=%HTTP_PROXY%
echo   HTTPS_PROXY=%HTTPS_PROXY%
echo.

:: 检查测试程序是否存在
if not exist "osg_compatibility_test.exe" (
    echo [错误] 找不到测试程序 osg_compatibility_test.exe
    echo 请确认您在正确的目录中运行此脚本
    pause
    exit /b 1
)

echo ========================================
echo 开始运行OSG接口兼容性测试...
echo ========================================

:: 创建测试报告目录
if not exist "test_reports" mkdir test_reports

echo.
echo [1/4] 运行基础兼容性测试...
echo ----------------------------------------
osg_compatibility_test.exe --basic
if %ERRORLEVEL% neq 0 (
    echo [警告] 基础测试出现问题，继续运行详细测试...
)

echo.
echo [2/4] 运行详细测试并生成HTML报告...
echo ----------------------------------------
osg_compatibility_test.exe --verbose --report test_reports\desktop_detailed_report.html
if %ERRORLEVEL% neq 0 (
    echo [警告] 详细测试出现问题
)

echo.
echo [3/4] 运行核心渲染功能测试...
echo ----------------------------------------
osg_compatibility_test.exe --test CoreRendering --report test_reports\desktop_core_rendering.html
if %ERRORLEVEL% neq 0 (
    echo [警告] 核心渲染测试出现问题
)

echo.
echo [4/4] 运行性能基准测试...
echo ----------------------------------------
osg_compatibility_test.exe --benchmark --report test_reports\desktop_benchmark.html
if %ERRORLEVEL% neq 0 (
    echo [警告] 性能测试出现问题
)

echo.
echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 测试报告已生成到 test_reports\ 目录:
if exist "test_reports\desktop_detailed_report.html" echo   ✓ desktop_detailed_report.html
if exist "test_reports\desktop_core_rendering.html" echo   ✓ desktop_core_rendering.html  
if exist "test_reports\desktop_benchmark.html" echo   ✓ desktop_benchmark.html

echo.
echo 下一步建议:
echo 1. 查看生成的HTML报告文件
echo 2. 运行WebAssembly版本测试进行对比
echo 3. 如有问题，请参考 OSG接口兼容性测试指导手册.md

echo.
echo 按任意键打开测试报告目录...
pause >nul
explorer test_reports
