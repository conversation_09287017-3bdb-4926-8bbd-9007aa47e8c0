prefix=${pcfiledir}/../..
##
## SPDX-License-Identifier: BSD-3-Clause
## Copyright (c) Contributors to the OpenEXR Project.
##

exec_prefix=${prefix}
libdir=${exec_prefix}/lib
includedir=${prefix}/../include
OpenEXR_includedir=${includedir}/OpenEXR
libsuffix=-3_3_d

Name: OpenEXR
Description: OpenEXR image library
Version: 3.3.4

Libs:  "-L${libdir}" "-lOpenEXR${libsuffix}" "-lOpenEXRUtil${libsuffix}" "-lOpenEXRCore${libsuffix}" "-lIex${libsuffix}" "-lIlmThread${libsuffix}"
Cflags: "-I${includedir}" "-I${OpenEXR_includedir}" 
Requires: Imath
Requires.private: libdeflate >= 1.24


