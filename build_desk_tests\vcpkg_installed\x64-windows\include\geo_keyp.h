/**********************************************************************
 *
 *  geo_keyp.h - private interface for GeoTIFF geokey tag parsing
 *
 *     Written by: <PERSON><PERSON>
 *
 *  copyright (c) 1995   <PERSON><PERSON>
 *
 *  Permission granted to use this software, so long as this copyright
 *  notice accompanies any products derived therefrom.
 **********************************************************************/

#ifndef LIBGEOTIFF_GEO_KEYP_H_
#define LIBGEOTIFF_GEO_KEYP_H_

#include <stdlib.h> /* for size_t */

#include "proj.h"

/*
 * This structure contains the internal program
 * representation of the key entry.
 */
struct GeoKey {
	int       gk_key;    /* GeoKey ID        */
	size_t    gk_size;   /* data byte size   */
	tagtype_t gk_type;   /* TIFF data type   */
	long      gk_count;  /* number of values */
	char*     gk_data;   /* pointer to data, or value */
};
typedef struct GeoKey GeoKey;

/*
 *  This structure represents the file-organization of
 *  the key entry. Note that it assumes that short entries
 *  are aligned along 2-byte boundaries.
 */
struct KeyEntry {
	pinfo_t ent_key;        /* GeoKey ID            */
	pinfo_t ent_location;   /* TIFF Tag ID or 0     */
	pinfo_t ent_count;      /* GeoKey value count   */
	pinfo_t ent_val_offset; /* value or tag offset  */
};
typedef struct KeyEntry KeyEntry;

/*
 * This is the header of the CoordSystemInfoTag. The 'Version'
 *  will only change if the CoorSystemInfoTag structure changes;
 *  The Major Revision will be incremented whenever a new set of
 *  Keys is added or changed, while the Minor revision will be
 *  incremented when only the set of Key-values is increased.
 */
struct KeyHeader{
	pinfo_t hdr_version;      /* GeoTIFF Version          */
	pinfo_t hdr_rev_major;    /* GeoKey Major Revision #  */
	pinfo_t hdr_rev_minor;    /* GeoKey Minor Revision #  */
	pinfo_t hdr_num_keys;     /* Number of GeoKeys        */
};
typedef struct KeyHeader KeyHeader;

/*
 * This structure holds temporary data while reading or writing
 *  the tags.
 */
struct TempKeyData {
    char   *tk_asciiParams;
    int     tk_asciiParamsLength;
    int     tk_asciiParamsOffset;
};
typedef struct TempKeyData TempKeyData;

struct gtiff;

#ifndef GTIF_PRINT_FUNC_FORMAT
#if defined(__GNUC__) && __GNUC__ >= 3
#define GTIF_PRINT_FUNC_FORMAT( format_idx, arg_idx )  __attribute__((__format__ (__printf__, format_idx, arg_idx)))
#else
#define GTIF_PRINT_FUNC_FORMAT( format_idx, arg_idx )
#endif
#endif

#ifndef GTERRORCALLBACK_DEFINED
#define GTERRORCALLBACK_DEFINED
/* Defined in both geotiff.h and geo_kep.h */
typedef void (*GTErrorCallback) (struct gtiff*,
                                 int level,
                                 const char* msg, ...) GTIF_PRINT_FUNC_FORMAT(3,4);
#endif

struct gtiff {
   tiff_t*    gt_tif;      /* TIFF file descriptor  */
   struct _TIFFMethod gt_methods;  /* TIFF i/o methods      */
   int        gt_flags;    /* file flags            */

   pinfo_t    gt_version;  /* GeoTIFF Version       */
   pinfo_t    gt_rev_major;/* GeoKey Key Revision   */
   pinfo_t    gt_rev_minor;/* GeoKey Code Revision  */

   int        gt_num_keys; /* number of keys        */
   GeoKey*    gt_keys;     /* array of keys         */
   int*       gt_keyindex; /* index of a key, if set*/
   int        gt_keymin;   /* smallest key set      */
   int        gt_keymax;   /* largest key set       */

   pinfo_t*   gt_short;    /* array of SHORT vals   */
   double*    gt_double;   /* array of DOUBLE vals  */
   int        gt_nshorts;  /* number of SHORT vals  */
   int        gt_ndoubles; /* number of DOUBLE vals */

   GTErrorCallback  gt_error_callback;
   void*      gt_user_data;

   PJ_CONTEXT *pj_context;      /* PROJ context */
   int         own_pj_context;  /* whether we own the PROJ context */

   char        szTmpBufferForGTIFValueNameEx[160];
};

typedef enum {
	FLAG_FILE_OPEN=1,
	FLAG_FILE_MODIFIED=2
} gtiff_flags;

#define MAX_KEYINDEX 65535   /* largest possible key    */
#define MAX_KEYS 100         /* maximum keys in a file  */
#define MAX_VALUES 1000      /* maximum values in a tag */

#endif /* LIBGEOTIFF_GEO_KEYP_H_ */
