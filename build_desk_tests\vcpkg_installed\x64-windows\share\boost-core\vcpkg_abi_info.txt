boost-assert ad7159e124c30a912194b478eca14416bd067113f018d2cdded5d63754baa9f0
boost-cmake 35be09501fff09e9c3011f020b913d5b0ded77762b657a92baa22a75c2d864cf
boost-config 369e403895802866afb8e38ef41f0f0c364f350286e63a275ef9c7a3203750f6
boost-headers b5bfe5da3e041f72bb5efdef6a56de1e2a1a4b102b35e1cb5acd80440adfd9d2
boost-static-assert 71a03f286d51fb85eb031d4bb94a6c656689252c6583d23059a32daafe080789
boost-throw-exception 47d10691f9a0168fba526246f016307e29a34e075ca6bac7c4a45807d74c15dd
cmake 3.30.1
features core
portfile.cmake b697ae1df7e70f05ceac98469af7148bc5d3f6c098356b8514ffad0228885ed3
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.2
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
vcpkg.json 0d2f041e41be2b1805ea8b61720235585adce5c6a0933ff3a2e6e663be177d93
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
