prefix=${pcfiledir}/../..
#***************************************************************************
#                                  _   _ ____  _
#  Project                     ___| | | |  _ \| |
#                             / __| | | | |_) | |
#                            | (__| |_| |  _ <| |___
#                             \___|\___/|_| \_\_____|
#
# Copyright (C) <PERSON>, <<EMAIL>>, et al.
#
# This software is licensed as described in the file COPYING, which
# you should have received as part of this distribution. The terms
# are also available at https://curl.se/docs/copyright.html.
#
# You may opt to use, copy, modify, merge, publish, distribute and/or sell
# copies of the Software, and permit persons to whom the Software is
# furnished to do so, under the terms of the COPYING file.
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY
# KIND, either express or implied.
#
# SPDX-License-Identifier: curl
#
###########################################################################

exec_prefix=${prefix}
libdir=${exec_prefix}/lib
includedir=${prefix}/../include
supported_protocols="DICT FILE FTP FTPS GOPHER GOPHERS HTTP HTTPS IMAP IMAPS IPFS IPNS MQTT POP3 POP3S RTSP SMB SMBS SMTP SMTPS TELNET TFTP"
supported_features="alt-svc AsynchDNS HSTS HTTPS-proxy IPv6 Kerberos Largefile libz NTLM SPNEGO SSL SSPI threadsafe Unicode UnixSockets"

Name: libcurl
URL: https://curl.se/
Description: Library to transfer files with HTTP, FTP, etc.
Version: 8.14.1-DEV
Requires: 
Requires.private: zlib
Libs: "-L${libdir}" -llibcurl-d
Libs.private:  -lws2_32 -lbcrypt -ladvapi32 -lcrypt32
Cflags: "-I${includedir}" 
Cflags.private: -DCURL_STATICLIB

