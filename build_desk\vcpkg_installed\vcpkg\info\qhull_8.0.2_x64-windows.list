x64-windows/
x64-windows/bin/
x64-windows/bin/qhull_r.dll
x64-windows/debug/
x64-windows/debug/bin/
x64-windows/debug/bin/qhull_rd.dll
x64-windows/debug/lib/
x64-windows/debug/lib/pkgconfig/
x64-windows/debug/lib/pkgconfig/qhull_r.pc
x64-windows/debug/lib/pkgconfig/qhullcpp.pc
x64-windows/debug/lib/qhull_rd.lib
x64-windows/debug/lib/qhullcpp_d.lib
x64-windows/include/
x64-windows/include/libqhull/
x64-windows/include/libqhull/DEPRECATED.txt
x64-windows/include/libqhull/geom.h
x64-windows/include/libqhull/io.h
x64-windows/include/libqhull/libqhull.h
x64-windows/include/libqhull/mem.h
x64-windows/include/libqhull/merge.h
x64-windows/include/libqhull/poly.h
x64-windows/include/libqhull/qhull_a.h
x64-windows/include/libqhull/qset.h
x64-windows/include/libqhull/random.h
x64-windows/include/libqhull/stat.h
x64-windows/include/libqhull/user.h
x64-windows/include/libqhull_r/
x64-windows/include/libqhull_r/geom_r.h
x64-windows/include/libqhull_r/io_r.h
x64-windows/include/libqhull_r/libqhull_r.h
x64-windows/include/libqhull_r/mem_r.h
x64-windows/include/libqhull_r/merge_r.h
x64-windows/include/libqhull_r/poly_r.h
x64-windows/include/libqhull_r/qhull_ra.h
x64-windows/include/libqhull_r/qset_r.h
x64-windows/include/libqhull_r/random_r.h
x64-windows/include/libqhull_r/stat_r.h
x64-windows/include/libqhull_r/user_r.h
x64-windows/include/libqhullcpp/
x64-windows/include/libqhullcpp/Coordinates.h
x64-windows/include/libqhullcpp/PointCoordinates.h
x64-windows/include/libqhullcpp/Qhull.h
x64-windows/include/libqhullcpp/QhullError.h
x64-windows/include/libqhullcpp/QhullFacet.h
x64-windows/include/libqhullcpp/QhullFacetList.h
x64-windows/include/libqhullcpp/QhullFacetSet.h
x64-windows/include/libqhullcpp/QhullHyperplane.h
x64-windows/include/libqhullcpp/QhullIterator.h
x64-windows/include/libqhullcpp/QhullLinkedList.h
x64-windows/include/libqhullcpp/QhullPoint.h
x64-windows/include/libqhullcpp/QhullPointSet.h
x64-windows/include/libqhullcpp/QhullPoints.h
x64-windows/include/libqhullcpp/QhullQh.h
x64-windows/include/libqhullcpp/QhullRidge.h
x64-windows/include/libqhullcpp/QhullSet.h
x64-windows/include/libqhullcpp/QhullSets.h
x64-windows/include/libqhullcpp/QhullStat.h
x64-windows/include/libqhullcpp/QhullUser.h
x64-windows/include/libqhullcpp/QhullVertex.h
x64-windows/include/libqhullcpp/QhullVertexSet.h
x64-windows/include/libqhullcpp/RboxPoints.h
x64-windows/include/libqhullcpp/RoadError.h
x64-windows/include/libqhullcpp/RoadLogEvent.h
x64-windows/include/libqhullcpp/RoadTest.h
x64-windows/include/libqhullcpp/functionObjects.h
x64-windows/lib/
x64-windows/lib/pkgconfig/
x64-windows/lib/pkgconfig/qhull_r.pc
x64-windows/lib/pkgconfig/qhullcpp.pc
x64-windows/lib/qhull_r.lib
x64-windows/lib/qhullcpp.lib
x64-windows/share/
x64-windows/share/qhull/
x64-windows/share/qhull/QhullConfig.cmake
x64-windows/share/qhull/QhullConfigVersion.cmake
x64-windows/share/qhull/QhullTargets-debug.cmake
x64-windows/share/qhull/QhullTargets-release.cmake
x64-windows/share/qhull/QhullTargets.cmake
x64-windows/share/qhull/copyright
x64-windows/share/qhull/usage
x64-windows/share/qhull/vcpkg.spdx.json
x64-windows/share/qhull/vcpkg_abi_info.txt
