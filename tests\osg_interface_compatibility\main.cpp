/* OSG Interface Compatibility Test Main Program
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#include "OSGInterfaceTestFramework.h"
#include "CoreRenderingTests.h"

#include <iostream>
#include <string>

#ifdef __EMSCRIPTEN__
#include <emscripten.h>
#include <emscripten/html5.h>
#endif

using namespace OSGCompatibilityTest;

// 全局测试框架实例
std::unique_ptr<OSGInterfaceTestFramework> g_testFramework;

void printUsage(const char* programName) {
    std::cout << "Usage: " << programName << " [options]" << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << "  --help, -h          Show this help message" << std::endl;
    std::cout << "  --suite <name>      Run specific test suite" << std::endl;
    std::cout << "  --report <file>     Generate HTML report (default: osg_compatibility_report.html)" << std::endl;
    std::cout << "  --verbose, -v       Enable verbose output" << std::endl;
    std::cout << std::endl;
    std::cout << "Available test suites:" << std::endl;
    std::cout << "  core-rendering      Core OSG rendering interface tests" << std::endl;
}

bool initializeTestFramework() {
    g_testFramework = std::make_unique<OSGInterfaceTestFramework>();
    
    if (!g_testFramework->initialize()) {
        std::cerr << "Failed to initialize test framework" << std::endl;
        return false;
    }
    
    // 添加测试套件
    g_testFramework->addTestSuite(createCoreRenderingTestSuite());
    
    return true;
}

void runTests(const std::string& suiteName = "") {
    if (!g_testFramework) {
        std::cerr << "Test framework not initialized" << std::endl;
        return;
    }
    
    std::cout << "\n=== OSG Interface Compatibility Test Suite ===" << std::endl;
    std::cout << "Platform: " << PlatformInfo::getPlatformName() << std::endl;
    std::cout << "OpenGL Version: " << PlatformInfo::getOpenGLVersion() << std::endl;
    std::cout << "Renderer: " << PlatformInfo::getRenderer() << std::endl;
    std::cout << "================================================" << std::endl;
    
    if (suiteName.empty()) {
        g_testFramework->runAllTests();
    } else {
        g_testFramework->runTestSuite(suiteName);
    }
}

void generateReport(const std::string& filename) {
    if (!g_testFramework) {
        std::cerr << "Test framework not initialized" << std::endl;
        return;
    }
    
    g_testFramework->generateReport(filename);
}

#ifdef __EMSCRIPTEN__
// WebAssembly环境下的主循环
void emscripten_main_loop() {
    // 在WebAssembly环境下，测试可能需要分帧执行
    // 这里暂时简单处理
    static bool testsRun = false;
    if (!testsRun) {
        runTests();
        generateReport("osg_compatibility_report.html");
        testsRun = true;
        emscripten_cancel_main_loop();
    }
}
#endif

int main(int argc, char* argv[]) {
    std::string suiteName;
    std::string reportFile = "osg_compatibility_report.html";
    bool verbose = false;
    
    // 解析命令行参数
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        
        if (arg == "--help" || arg == "-h") {
            printUsage(argv[0]);
            return 0;
        } else if (arg == "--suite" && i + 1 < argc) {
            suiteName = argv[++i];
        } else if (arg == "--report" && i + 1 < argc) {
            reportFile = argv[++i];
        } else if (arg == "--verbose" || arg == "-v") {
            verbose = true;
        } else {
            std::cerr << "Unknown option: " << arg << std::endl;
            printUsage(argv[0]);
            return 1;
        }
    }
    
    std::cout << "OSG Interface Compatibility Test Suite" << std::endl;
    std::cout << "=======================================" << std::endl;
    
    // 初始化测试框架
    if (!initializeTestFramework()) {
        std::cerr << "Failed to initialize test framework" << std::endl;
        return 1;
    }
    
#ifdef __EMSCRIPTEN__
    // WebAssembly环境下使用主循环
    std::cout << "Running in WebAssembly environment" << std::endl;
    emscripten_set_main_loop(emscripten_main_loop, 60, 1);
#else
    // 桌面环境下直接运行
    std::cout << "Running in Desktop environment" << std::endl;
    
    try {
        runTests(suiteName);
        generateReport(reportFile);
        
        // 输出最终统计
        auto stats = g_testFramework->getStatistics();
        std::cout << "\n=== Final Results ===" << std::endl;
        std::cout << "Total Tests: " << stats.totalTests << std::endl;
        std::cout << "Passed: " << stats.passedTests << std::endl;
        std::cout << "Failed: " << stats.failedTests << std::endl;
        std::cout << "Skipped: " << stats.skippedTests << std::endl;
        std::cout << "Not Supported: " << stats.notSupportedTests << std::endl;
        std::cout << "Success Rate: " << (stats.totalTests > 0 ? 
            (100.0 * stats.passedTests / stats.totalTests) : 0.0) << "%" << std::endl;
        
        // 返回适当的退出码
        return (stats.failedTests == 0) ? 0 : 1;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception during test execution: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown exception during test execution" << std::endl;
        return 1;
    }
#endif
    
    return 0;
}
