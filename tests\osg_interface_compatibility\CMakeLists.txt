# OSG Interface Compatibility Test Framework
# Copyright 2025 Pelican Mapping
# MIT License

cmake_minimum_required(VERSION 3.16)

project(OSGInterfaceCompatibilityTests)

# 设置C++标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译器特定设置
if(MSVC)
    add_compile_options(/utf-8)
    add_compile_options(/W4)
else()
    add_compile_options(-Wall -Wextra)
endif()

# 查找依赖
find_package(OpenSceneGraph REQUIRED COMPONENTS osg osgDB osgViewer osgGA osgUtil)

# 包含目录
include_directories(${OPENSCENEGRAPH_INCLUDE_DIRS})

# 源文件
set(FRAMEWORK_SOURCES
    OSGInterfaceTestFramework.h
    OSGInterfaceTestFramework.cpp
    CoreRenderingTests.h
    CoreRenderingTests.cpp
    main.cpp
)

# 创建可执行文件
if(EMSCRIPTEN)
    # WebAssembly构建设置
    add_executable(osg_compatibility_test ${FRAMEWORK_SOURCES})
    
    # Emscripten特定设置
    set_target_properties(osg_compatibility_test PROPERTIES
        SUFFIX ".html"
        LINK_FLAGS "-s USE_WEBGL2=1 -s FULL_ES3=1 -s WASM=1 -s ALLOW_MEMORY_GROWTH=1 --shell-file ${CMAKE_CURRENT_SOURCE_DIR}/shell.html"
    )
    
    # 添加预加载文件（如果需要）
    # set_target_properties(osg_compatibility_test PROPERTIES
    #     LINK_FLAGS "${LINK_FLAGS} --preload-file data@/data"
    # )
    
else()
    # 桌面构建设置
    add_executable(osg_compatibility_test ${FRAMEWORK_SOURCES})
    
    # 设置输出目录
    set_target_properties(osg_compatibility_test PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    )
endif()

# 链接库
target_link_libraries(osg_compatibility_test
    ${OPENSCENEGRAPH_LIBRARIES}
)

# 平台特定链接库
if(WIN32)
    target_link_libraries(osg_compatibility_test
        opengl32
        glu32
    )
elseif(UNIX AND NOT APPLE)
    target_link_libraries(osg_compatibility_test
        GL
        GLU
        X11
    )
elseif(APPLE)
    find_library(OPENGL_LIBRARY OpenGL)
    target_link_libraries(osg_compatibility_test
        ${OPENGL_LIBRARY}
    )
endif()

# 编译定义
if(EMSCRIPTEN)
    target_compile_definitions(osg_compatibility_test PRIVATE
        __EMSCRIPTEN__
        GL_GLEXT_PROTOTYPES
    )
endif()

# 安装规则
if(NOT EMSCRIPTEN)
    install(TARGETS osg_compatibility_test
        RUNTIME DESTINATION bin
    )
    
    # 安装测试数据（如果有）
    # install(DIRECTORY data/
    #     DESTINATION share/osg_compatibility_test/data
    # )
endif()

# 添加自定义目标用于运行测试
if(NOT EMSCRIPTEN)
    add_custom_target(run_compatibility_tests
        COMMAND osg_compatibility_test
        DEPENDS osg_compatibility_test
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/bin
        COMMENT "Running OSG interface compatibility tests"
    )
    
    add_custom_target(run_compatibility_tests_verbose
        COMMAND osg_compatibility_test --verbose
        DEPENDS osg_compatibility_test
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/bin
        COMMENT "Running OSG interface compatibility tests (verbose)"
    )
endif()

# 生成编译数据库（用于IDE支持）
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# 打印配置信息
message(STATUS "OSG Interface Compatibility Tests Configuration:")
message(STATUS "  Platform: ${CMAKE_SYSTEM_NAME}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")

if(EMSCRIPTEN)
    message(STATUS "  Emscripten: YES")
    message(STATUS "  Output: osg_compatibility_test.html")
else()
    message(STATUS "  Emscripten: NO")
    message(STATUS "  Output: osg_compatibility_test")
endif()

message(STATUS "  OpenSceneGraph:")
message(STATUS "    Include: ${OPENSCENEGRAPH_INCLUDE_DIRS}")
message(STATUS "    Libraries: ${OPENSCENEGRAPH_LIBRARIES}")

# 帮助信息
if(NOT EMSCRIPTEN)
    message(STATUS "")
    message(STATUS "Build and run tests:")
    message(STATUS "  make osg_compatibility_test")
    message(STATUS "  make run_compatibility_tests")
    message(STATUS "  make run_compatibility_tests_verbose")
    message(STATUS "")
    message(STATUS "Manual execution:")
    message(STATUS "  ./bin/osg_compatibility_test --help")
    message(STATUS "  ./bin/osg_compatibility_test --suite core-rendering")
    message(STATUS "  ./bin/osg_compatibility_test --report custom_report.html")
endif()
