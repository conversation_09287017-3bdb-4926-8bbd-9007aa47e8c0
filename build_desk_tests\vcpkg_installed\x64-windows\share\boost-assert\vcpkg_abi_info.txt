boost-cmake 35be09501fff09e9c3011f020b913d5b0ded77762b657a92baa22a75c2d864cf
boost-config 369e403895802866afb8e38ef41f0f0c364f350286e63a275ef9c7a3203750f6
boost-headers b5bfe5da3e041f72bb5efdef6a56de1e2a1a4b102b35e1cb5acd80440adfd9d2
cmake 3.30.1
features core
portfile.cmake 76956736abe78adf1ced8d96b131a05cba622e169d544d5919bab915dac09c43
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.2
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
vcpkg.json 2f454ce9b0ab15f350f1bdf884acffbd5272f4f1e3d4de1e01c5d6c3b9698671
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
