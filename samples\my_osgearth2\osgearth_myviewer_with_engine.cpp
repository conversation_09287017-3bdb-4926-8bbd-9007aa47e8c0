// osgEarth WebAssembly 版本 - 集成新的渲染引擎
// 基于 osgearth_myviewer_wasm_sdl_fixed.cpp，使用我们的新渲染引擎架构

#ifdef EMSCRIPTEN
#define _LIBCPP_HAS_NO_PRAGMA_SYSTEM_HEADER
#define _LIBCPP_DISABLE_AVAILABILITY
#endif

#ifdef _WIN32
#define _USE_MATH_DEFINES
#define NOMINMAX
#endif

#include <iostream>
#include <memory>
#include <string>
#include <typeinfo>

// 新的渲染引擎
#include <osgEarth/RenderingEngine.h>

// OSG核心头文件
#include <osg/ref_ptr>
#include <osg/Group>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/Array>
#include <osg/PrimitiveSet>
#include <osg/NodeVisitor>
#include <osg/StateSet>
#include <osg/Material>
#include <osgDB/Registry>

// osgEarth 头文件
#include <osgEarth/Registry>
#include <osgEarth/TerrainEngineNode>
#include <osgEarth/TerrainOptions>
#include <osgEarth/Map>
#include <osgEarth/MapNode>
#include <osgEarth/ImageLayer>
#include <osgEarth/Config>
#include <osgEarth/XYZ>
#include <osgEarth/EarthManipulator>
#include <osgEarth/Profile>

// 平台相关头文件
#ifdef EMSCRIPTEN
#include <SDL2/SDL.h>
#include <emscripten.h>
#include <emscripten/html5.h>
#include <emscripten/threading.h>
#else
#include <SDL.h>
#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif
#endif

#define DEBUG_LOG(msg) std::cout << "[DEBUG] " << msg << std::endl
#define ERROR_LOG(msg) std::cerr << "[ERROR] " << msg << std::endl
#define INFO_LOG(msg) std::cout << "[INFO] " << msg << std::endl

using namespace osgEarth;

// 前向声明
void createOsgEarthShader();
void createFallbackGeometry();

// 全局变量
struct AppContext
{
    std::unique_ptr<IRenderingEngine> engine;
    osg::ref_ptr<osg::Group> rootNode;
    osg::ref_ptr<osgEarth::MapNode> mapNode;

    SDL_Window *window;
    SDL_GLContext context;
    bool shouldExit;

    // 渲染资源 ID
    unsigned int earthGeometryId = 0;
    unsigned int earthShaderId = 0;

    AppContext() : shouldExit(false), window(nullptr), context(nullptr) {}
};

static AppContext *g_appContext = nullptr;

// 鼠标交互状态
struct MouseState
{
    bool leftButtonDown = false;
    bool rightButtonDown = false;
    int lastX = 0;
    int lastY = 0;
    float cameraDistance = 3.0f;
    float cameraRotationX = 0.0f;
    float cameraRotationY = 0.0f;
    float cameraZoom = 1.0f;
} g_mouseState;

/**
 * 检查 osgEarth 插件状态
 */
void checkOsgEarthPlugins()
{
    // 简化的插件检查
    osgDB::Registry *registry = osgDB::Registry::instance();
    if (!registry)
    {
        ERROR_LOG("❌ osgDB::Registry 未初始化");
        return;
    }
    INFO_LOG("✅ osgEarth 插件系统就绪");
}

/**
 * 测试基础 osgEarth 功能
 */
void testOsgEarthBasics()
{
    try
    {
        // 快速验证核心功能
        osg::ref_ptr<osgEarth::Map> map = new osgEarth::Map();
        osg::ref_ptr<osgEarth::MapNode> mapNode = new osgEarth::MapNode(map.get());

        if (map.valid() && mapNode.valid())
        {
            INFO_LOG("✅ osgEarth 核心功能验证通过");
        }
        else
        {
            ERROR_LOG("❌ osgEarth 核心功能验证失败");
        }
    }
    catch (const std::exception &e)
    {
        ERROR_LOG("❌ osgEarth 功能测试异常: " << e.what());
    }
}

/**
 * 创建 osgEarth 地球场景
 */
bool createOsgEarthScene()
{
    INFO_LOG("=== 创建 osgEarth 地球场景 ===");

    try
    {
        // 1. 创建地图
        osg::ref_ptr<osgEarth::Map> map = new osgEarth::Map();
        if (!map.valid())
        {
            ERROR_LOG("❌ 地图创建失败");
            return false;
        }

        // 1.1 添加谷歌地图XYZ瓦片图层
        try
        {
            osgEarth::XYZImageLayer::Options xyzOptions;
            xyzOptions.url() = osgEarth::URI("https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}");
            xyzOptions.name() = "Google Satellite";

            osg::ref_ptr<osgEarth::XYZImageLayer> xyzLayer = new osgEarth::XYZImageLayer(xyzOptions);
            xyzLayer->setProfile(osgEarth::Profile::create(osgEarth::Profile::SPHERICAL_MERCATOR));
            map->addLayer(xyzLayer.get());
            INFO_LOG("✅ 谷歌卫星图层已添加");
        }
        catch (const std::exception &e)
        {
            INFO_LOG("⚠️ 谷歌地图图层添加失败，使用默认地球");
        }

        // 2. 设置地形选项
        osgEarth::TerrainOptions terrainOptions;

#ifdef EMSCRIPTEN
        // WebGL 兼容性设置
        terrainOptions.enableBlending() = false;
        terrainOptions.gpuTessellation() = false;
        terrainOptions.morphImagery() = false;
        terrainOptions.morphTerrain() = false;
        terrainOptions.enableLighting() = false;
        terrainOptions.castShadows() = false;
        terrainOptions.normalizeEdges() = false;
        terrainOptions.maxLOD() = 10;
#endif

        // 3. 创建 MapNode
        g_appContext->mapNode = new osgEarth::MapNode(map.get(), terrainOptions);
        if (!g_appContext->mapNode.valid())
        {
            ERROR_LOG("❌ MapNode 创建失败");
            return false;
        }

        // 4. 设置基础渲染状态
        osg::ref_ptr<osg::StateSet> stateSet = g_appContext->mapNode->getOrCreateStateSet();

#ifdef EMSCRIPTEN
        stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);
        stateSet->setMode(GL_CULL_FACE, osg::StateAttribute::ON);

        osg::ref_ptr<osg::Material> material = new osg::Material();
        material->setDiffuse(osg::Material::FRONT_AND_BACK, osg::Vec4(0.0f, 0.8f, 0.0f, 1.0f));
        stateSet->setAttributeAndModes(material.get(), osg::StateAttribute::ON);
#endif

        INFO_LOG("✅ osgEarth 数字地球创建成功");
        return true;
    }
    catch (const std::exception &e)
    {
        ERROR_LOG("❌ osgEarth 场景创建异常: " << e.what());
        return false;
    }
}

/**
 * OSG 场景图访问器 - 提取几何体数据
 */
class GeometryExtractor : public osg::NodeVisitor
{
public:
    GeometryExtractor() : osg::NodeVisitor(osg::NodeVisitor::TRAVERSE_ALL_CHILDREN) {}

    virtual void apply(osg::Geode &geode) override
    {
        for (unsigned int i = 0; i < geode.getNumDrawables(); ++i)
        {
            osg::Drawable *drawable = geode.getDrawable(i);
            osg::Geometry *geometry = drawable->asGeometry();
            if (geometry)
            {
                extractGeometry(geometry);
            }
        }
        traverse(geode);
    }

    virtual void apply(osg::Geometry &geometry) override
    {
        extractGeometry(&geometry);
        traverse(geometry);
    }

    void extractGeometry(osg::Geometry *geometry)
    {
        if (!geometry)
            return;

        // 提取顶点数据
        osg::Vec3Array *vertices = dynamic_cast<osg::Vec3Array *>(geometry->getVertexArray());
        if (vertices)
        {
            for (unsigned int i = 0; i < vertices->size(); ++i)
            {
                extractedVertices.push_back((*vertices)[i]);
            }
        }

        // 提取法线数据
        osg::Vec3Array *normals = dynamic_cast<osg::Vec3Array *>(geometry->getNormalArray());
        if (normals)
        {
            for (unsigned int i = 0; i < normals->size(); ++i)
            {
                extractedNormals.push_back((*normals)[i]);
            }
        }

        // 提取纹理坐标
        osg::Vec2Array *texCoords = dynamic_cast<osg::Vec2Array *>(geometry->getTexCoordArray(0));
        if (texCoords)
        {
            for (unsigned int i = 0; i < texCoords->size(); ++i)
            {
                extractedTexCoords.push_back((*texCoords)[i]);
            }
        }

        // 提取索引数据
        for (unsigned int i = 0; i < geometry->getNumPrimitiveSets(); ++i)
        {
            osg::PrimitiveSet *primitiveSet = geometry->getPrimitiveSet(i);
            if (primitiveSet)
            {
                extractIndices(primitiveSet);
            }
        }

        geometryCount++;
    }

    void extractIndices(osg::PrimitiveSet *primitiveSet)
    {
        switch (primitiveSet->getMode())
        {
        case osg::PrimitiveSet::TRIANGLES:
        {
            osg::DrawElementsUInt *drawElements = dynamic_cast<osg::DrawElementsUInt *>(primitiveSet);
            if (drawElements)
            {
                for (unsigned int i = 0; i < drawElements->size(); ++i)
                {
                    extractedIndices.push_back((*drawElements)[i]);
                }
            }
            break;
        }
        case osg::PrimitiveSet::TRIANGLE_STRIP:
        {
            // 将三角形条带转换为三角形
            osg::DrawElementsUInt *drawElements = dynamic_cast<osg::DrawElementsUInt *>(primitiveSet);
            if (drawElements && drawElements->size() >= 3)
            {
                for (unsigned int i = 0; i < drawElements->size() - 2; ++i)
                {
                    if (i % 2 == 0)
                    {
                        extractedIndices.push_back((*drawElements)[i]);
                        extractedIndices.push_back((*drawElements)[i + 1]);
                        extractedIndices.push_back((*drawElements)[i + 2]);
                    }
                    else
                    {
                        extractedIndices.push_back((*drawElements)[i]);
                        extractedIndices.push_back((*drawElements)[i + 2]);
                        extractedIndices.push_back((*drawElements)[i + 1]);
                    }
                }
            }
            break;
        }
        default:
            INFO_LOG("不支持的图元类型: " << primitiveSet->getMode());
            break;
        }
    }

    std::vector<osg::Vec3> extractedVertices;
    std::vector<osg::Vec3> extractedNormals;
    std::vector<osg::Vec2> extractedTexCoords;
    std::vector<unsigned int> extractedIndices;
    int geometryCount = 0;
};

/**
 * 从 osgEarth 场景中提取几何体数据
 */
void extractOsgEarthGeometry()
{
    INFO_LOG("从 osgEarth 场景中提取几何体数据...");

    if (!g_appContext->mapNode.valid())
    {
        ERROR_LOG("❌ MapNode 无效，无法提取几何体");
        return;
    }

    // 创建几何体提取器
    GeometryExtractor extractor;

    // 遍历 MapNode 场景图
    g_appContext->mapNode->accept(extractor);

    INFO_LOG("提取完成: " << extractor.geometryCount << " 个几何体, "
                          << extractor.extractedVertices.size() << " 个顶点, "
                          << extractor.extractedIndices.size() << " 个索引");

    if (extractor.extractedVertices.empty())
    {
        ERROR_LOG("❌ 未提取到任何几何体数据，使用备用几何体");
        createFallbackGeometry();
        return;
    }

    // 创建 osgEarth 兼容的着色器
    createOsgEarthShader();

    // 将提取的数据转换为渲染引擎格式
    GeometryDesc geom;
    geom.primitiveType = GL_TRIANGLES;

    geom.vertices = extractor.extractedVertices;
    geom.normals = extractor.extractedNormals.empty() ? extractor.extractedVertices : extractor.extractedNormals;
    geom.texCoords = extractor.extractedTexCoords;
    geom.indices = extractor.extractedIndices;

    // 如果没有纹理坐标，生成默认的
    if (geom.texCoords.empty())
    {
        for (size_t i = 0; i < geom.vertices.size(); ++i)
        {
            geom.texCoords.push_back(osg::Vec2(0.5f, 0.5f));
        }
    }

    // 创建几何体
    g_appContext->earthGeometryId = g_appContext->engine->createGeometry(geom);

    INFO_LOG("✅ osgEarth 几何体创建完成: " << g_appContext->earthGeometryId
                                            << " (" << geom.vertices.size() << " vertices, " << geom.indices.size() << " indices)");
}

/**
 * 创建 osgEarth 兼容的着色器
 */
void createOsgEarthShader()
{
    ShaderDesc shader;
    shader.vertexSource = R"(#version 300 es
precision highp float;
layout(location = 0) in vec3 a_position;
layout(location = 1) in vec3 a_normal;
layout(location = 2) in vec2 a_texCoord;
uniform mat4 u_projectionMatrix;
uniform mat4 u_viewMatrix;
uniform mat4 u_modelMatrix;
out vec3 v_worldPos;
out vec3 v_normal;
out vec2 v_texCoord;
void main() {
    vec4 worldPos = u_modelMatrix * vec4(a_position, 1.0);
    v_worldPos = worldPos.xyz;
    v_normal = normalize((u_modelMatrix * vec4(a_normal, 0.0)).xyz);
    v_texCoord = a_texCoord;
    mat4 mvp = u_projectionMatrix * u_viewMatrix * u_modelMatrix;
    gl_Position = mvp * vec4(a_position, 1.0);
}
)";

    shader.fragmentSource = R"(#version 300 es
precision highp float;
in vec3 v_worldPos;
in vec3 v_normal;
in vec2 v_texCoord;
uniform sampler2D u_texture;
uniform bool u_hasTexture;
out vec4 fragColor;
void main() {
    vec3 baseColor;

    if (u_hasTexture) {
        // 使用纹理贴图
        baseColor = texture(u_texture, v_texCoord).rgb;
    } else {
        // 使用程序化地球颜色
        baseColor = vec3(0.3, 0.7, 0.3); // 地球绿色

        // 基于纹理坐标的变化
        vec3 texColor = vec3(v_texCoord.x, v_texCoord.y, 0.5);
        baseColor = mix(baseColor, texColor, 0.3);

        // 高度基于的颜色变化
        float height = length(v_worldPos);
        vec3 heightColor = mix(vec3(0.2, 0.4, 0.8), vec3(0.8, 0.6, 0.2), clamp(height - 0.99, 0.0, 1.0) * 10.0);
        baseColor = mix(baseColor, heightColor, 0.2);
    }

    // 简单光照
    vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
    float NdotL = max(dot(normalize(v_normal), lightDir), 0.2);

    fragColor = vec4(baseColor * NdotL, 1.0);
}
)";

    g_appContext->earthShaderId = g_appContext->engine->createShaderProgram(shader);
    INFO_LOG("✅ osgEarth 兼容着色器创建完成: " << g_appContext->earthShaderId);
}

/**
 * 创建备用几何体（当无法提取 osgEarth 几何体时）
 */
void createFallbackGeometry()
{
    INFO_LOG("创建备用地球几何体...");

    createOsgEarthShader();

    // 创建简单的地球几何体作为备用
    GeometryDesc geom;
    geom.primitiveType = GL_TRIANGLES;

    const int segments = 32;
    const float radius = 1.0f;

    for (int lat = 0; lat <= segments; ++lat)
    {
        float theta = lat * M_PI / segments;
        float sinTheta = sin(theta);
        float cosTheta = cos(theta);

        for (int lon = 0; lon <= segments; ++lon)
        {
            float phi = lon * 2 * M_PI / segments;
            float sinPhi = sin(phi);
            float cosPhi = cos(phi);

            float x = radius * sinTheta * cosPhi;
            float y = radius * cosTheta;
            float z = radius * sinTheta * sinPhi;

            geom.vertices.push_back(osg::Vec3(x, y, z));
            geom.normals.push_back(osg::Vec3(x, y, z));
            geom.texCoords.push_back(osg::Vec2((float)lon / segments, (float)lat / segments));
        }
    }

    // 生成索引
    for (int lat = 0; lat < segments; ++lat)
    {
        for (int lon = 0; lon < segments; ++lon)
        {
            int current = lat * (segments + 1) + lon;
            int next = current + segments + 1;

            geom.indices.push_back(current);
            geom.indices.push_back(next);
            geom.indices.push_back(current + 1);

            geom.indices.push_back(current + 1);
            geom.indices.push_back(next);
            geom.indices.push_back(next + 1);
        }
    }

    g_appContext->earthGeometryId = g_appContext->engine->createGeometry(geom);
    INFO_LOG("✅ 备用几何体创建完成: " << g_appContext->earthGeometryId);
}

/**
 * 初始化SDL
 */
bool initializeSDL()
{
    DEBUG_LOG("Initializing SDL...");

    if (SDL_Init(SDL_INIT_VIDEO) < 0)
    {
        ERROR_LOG("SDL initialization failed: " << SDL_GetError());
        return false;
    }

    // 设置OpenGL属性
#ifdef EMSCRIPTEN
    // WebAssembly版本使用OpenGL ES
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_ES);
#else
    // 桌面版使用标准OpenGL
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_CORE);
#endif
    SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
    SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);
    SDL_GL_SetAttribute(SDL_GL_STENCIL_SIZE, 8);

    // 创建窗口
    g_appContext->window = SDL_CreateWindow(
        "osgEarth WebAssembly - 新渲染引擎版本",
        SDL_WINDOWPOS_CENTERED, SDL_WINDOWPOS_CENTERED,
        800, 600,
        SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN);

    if (!g_appContext->window)
    {
        ERROR_LOG("Window creation failed: " << SDL_GetError());
        return false;
    }

    // 创建OpenGL上下文
    g_appContext->context = SDL_GL_CreateContext(g_appContext->window);
    if (!g_appContext->context)
    {
        ERROR_LOG("OpenGL context creation failed: " << SDL_GetError());
        return false;
    }

    SDL_GL_MakeCurrent(g_appContext->window, g_appContext->context);
    SDL_GL_SetSwapInterval(1);

    DEBUG_LOG("SDL initialized successfully");
    return true;
}

/**
 * 处理鼠标事件
 */
void handleMouseEvent(SDL_Event &event)
{
    switch (event.type)
    {
    case SDL_MOUSEBUTTONDOWN:
        if (event.button.button == SDL_BUTTON_LEFT)
        {
            g_mouseState.leftButtonDown = true;
            g_mouseState.lastX = event.button.x;
            g_mouseState.lastY = event.button.y;
        }
        else if (event.button.button == SDL_BUTTON_RIGHT)
        {
            g_mouseState.rightButtonDown = true;
            g_mouseState.lastX = event.button.x;
            g_mouseState.lastY = event.button.y;
        }
        break;

    case SDL_MOUSEBUTTONUP:
        if (event.button.button == SDL_BUTTON_LEFT)
        {
            g_mouseState.leftButtonDown = false;
        }
        else if (event.button.button == SDL_BUTTON_RIGHT)
        {
            g_mouseState.rightButtonDown = false;
        }
        break;

    case SDL_MOUSEMOTION:
        if (g_mouseState.leftButtonDown)
        {
            // 左键拖拽：旋转地球（类似 Google Earth）
            int deltaX = event.motion.x - g_mouseState.lastX;
            int deltaY = event.motion.y - g_mouseState.lastY;

            // 水平旋转（经度）
            g_mouseState.cameraRotationY += deltaX * 0.005f;

            // 垂直旋转（纬度）- 反向以符合直觉
            g_mouseState.cameraRotationX -= deltaY * 0.005f;

            // 限制垂直旋转角度（防止翻转）
            g_mouseState.cameraRotationX = std::max(-1.4f, std::min(1.4f, g_mouseState.cameraRotationX));
        }
        else if (g_mouseState.rightButtonDown)
        {
            // 右键拖拽：缩放
            int deltaY = event.motion.y - g_mouseState.lastY;
            g_mouseState.cameraDistance += deltaY * 0.02f;
            g_mouseState.cameraDistance = std::max(1.2f, std::min(15.0f, g_mouseState.cameraDistance));
        }

        g_mouseState.lastX = event.motion.x;
        g_mouseState.lastY = event.motion.y;
        break;

    case SDL_MOUSEWHEEL:
        // 鼠标滚轮：快速缩放
        g_mouseState.cameraDistance -= event.wheel.y * 0.3f;
        g_mouseState.cameraDistance = std::max(1.2f, std::min(15.0f, g_mouseState.cameraDistance));
        break;
    }
}

/**
 * 设置相机
 */
void setupCamera()
{
    // 透视投影
    float fov = 45.0f;
    float aspect = 800.0f / 600.0f;
    float near = 0.1f;
    float far = 100.0f;

    osg::Matrix projection;
    projection.makePerspective(fov, aspect, near, far);
    g_appContext->engine->setProjectionMatrix(projection);

    // 使用鼠标控制的相机位置（球坐标系）
    float distance = g_mouseState.cameraDistance;
    float elevation = g_mouseState.cameraRotationX; // 仰角（纬度）
    float azimuth = g_mouseState.cameraRotationY;   // 方位角（经度）

    // 球坐标转换为笛卡尔坐标
    float x = distance * cos(elevation) * sin(azimuth);
    float y = distance * sin(elevation);
    float z = distance * cos(elevation) * cos(azimuth);

    osg::Vec3 eye(x, y, z);
    osg::Vec3 center(0, 0, 0); // 地球中心

    // 计算正确的上向量
    osg::Vec3 up;
    if (abs(elevation) > 1.3f) // 接近极点时
    {
        up = osg::Vec3(0, 0, 1); // 使用Z轴作为上向量
    }
    else
    {
        up = osg::Vec3(0, 1, 0); // 正常情况使用Y轴
    }

    osg::Matrix view;
    view.makeLookAt(eye, center, up);
    g_appContext->engine->setViewMatrix(view);

    // 模型矩阵（地球不再自转，由用户控制）
    osg::Matrix model;
    model.makeIdentity();
    g_appContext->engine->setModelMatrix(model);
}

/**
 * 主循环 - 使用新的渲染引擎
 */
void mainLoop()
{
#ifdef EMSCRIPTEN
    // 检查是否在主线程中
    if (!emscripten_is_main_browser_thread())
    {
        ERROR_LOG("⚠️ 主循环不在主浏览器线程中执行");
        return;
    }
#endif

    // 处理SDL事件
    SDL_Event event;
    while (SDL_PollEvent(&event))
    {
        if (event.type == SDL_QUIT)
        {
            g_appContext->shouldExit = true;
        }
        else
        {
            // 处理鼠标事件
            handleMouseEvent(event);
        }
    }

    // 使用新的渲染引擎渲染
    if (g_appContext->engine)
    {
        try
        {
            g_appContext->engine->beginFrame();
            g_appContext->engine->clear(osg::Vec4(0.0f, 0.0f, 0.1f, 1.0f)); // 深空背景

            // 设置相机
            setupCamera();

            // 渲染地球
            if (g_appContext->earthGeometryId > 0 && g_appContext->earthShaderId > 0)
            {
                g_appContext->engine->renderGeometry(g_appContext->earthGeometryId, g_appContext->earthShaderId);
            }

            g_appContext->engine->endFrame();

            // 显示统计
            static int frameCount = 0;
            if (++frameCount % 60 == 0)
            {
                const auto &stats = g_appContext->engine->getStats();
                INFO_LOG("🌍 osgEarth 渲染统计: " << stats.drawCalls << " draws, "
                                                  << stats.triangles << " triangles");

                if (g_appContext->mapNode.valid())
                {
                    INFO_LOG("📍 MapNode 状态: 有效，地形引擎: "
                             << (g_appContext->mapNode->getTerrainEngine() ? "已附加" : "未附加"));
                }
            }
        }
        catch (const std::exception &e)
        {
            ERROR_LOG("❌ 渲染异常: " << e.what());
        }
    }

#ifndef EMSCRIPTEN
    // 桌面版本需要交换缓冲区
    SDL_GL_SwapWindow(g_appContext->window);
#endif
}

/**
 * 主函数
 */
int main(int argc, char *argv[])
{
#ifndef EMSCRIPTEN
    // 对于桌面版本，分配控制台用于调试输出
    if (AllocConsole())
    {
        freopen_s((FILE **)stdout, "CONOUT$", "w", stdout);
        freopen_s((FILE **)stderr, "CONOUT$", "w", stderr);
        freopen_s((FILE **)stdin, "CONIN$", "r", stdin);
        SetConsoleTitle("osgEarth 新渲染引擎版本 - Debug Output");
    }
#endif

    INFO_LOG("osgEarth WebAssembly 数字地球启动中...");

    // 创建应用上下文
    g_appContext = new AppContext();

    // 初始化 osgEarth 和 SDL
    osgEarth::initialize();
    if (!initializeSDL())
    {
        delete g_appContext;
        return -1;
    }

    // 创建渲染引擎
#ifdef EMSCRIPTEN
    g_appContext->engine = RenderingEngineFactory::create(RenderingEngineFactory::WEBGL);
#else
    g_appContext->engine = RenderingEngineFactory::create(RenderingEngineFactory::DESKTOP_OPENGL);
#endif

    if (!g_appContext->engine)
    {
        g_appContext->engine = RenderingEngineFactory::create(RenderingEngineFactory::CONSOLE_DEBUG);
    }

    if (!g_appContext->engine->initialize())
    {
        ERROR_LOG("❌ 渲染引擎初始化失败");
        delete g_appContext;
        return -1;
    }

    INFO_LOG("✅ 渲染引擎就绪: " << g_appContext->engine->getTypeName());

    // 检查和创建 osgEarth 场景
    checkOsgEarthPlugins();
    testOsgEarthBasics();

    bool sceneCreated = createOsgEarthScene();
    if (sceneCreated)
    {
        extractOsgEarthGeometry();
        INFO_LOG("✅ osgEarth 数字地球就绪");
    }
    else
    {
        ERROR_LOG("❌ osgEarth 场景创建失败");
    }

    // 7. 启动主循环
#ifdef EMSCRIPTEN
    emscripten_set_main_loop(mainLoop, 60, 1); // 60 FPS
#else
    while (!g_appContext->shouldExit)
    {
        mainLoop();
        std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
    }
#endif

    // 清理资源
    if (g_appContext->context)
        SDL_GL_DeleteContext(g_appContext->context);
    if (g_appContext->window)
        SDL_DestroyWindow(g_appContext->window);
    delete g_appContext;
    SDL_Quit();

    return 0;
}
