@echo off
echo ========================================
echo Building OSG Interface Compatibility Tests for WebAssembly
echo ========================================

:: 设置代理环境变量（用于下载瓦片）
set HTTP_PROXY=http://127.0.0.1:10809
set HTTPS_PROXY=http://127.0.0.1:10809
set http_proxy=http://127.0.0.1:10809
set https_proxy=http://127.0.0.1:10809

echo Setting up Emscripten environment...
set EMSDK_ROOT=C:\dev\emsdk
call "%EMSDK_ROOT%\emsdk_env.bat"

if %ERRORLEVEL% neq 0 (
    echo Failed to setup Emscripten environment!
    echo Please ensure Emscripten is installed at C:\dev\emsdk
    pause
    exit /b 1
)

set WASM_DEP_ROOT=F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep
set BUILD_DIR=tests\osg_interface_compatibility\build_wasm
set REDIST_DIR=tests\osg_interface_compatibility\redist_wasm

echo Cleaning previous build...
if exist %BUILD_DIR% rmdir /s /q %BUILD_DIR%
if exist %REDIST_DIR% rmdir /s /q %REDIST_DIR%

echo Creating build directory...
mkdir %BUILD_DIR%
mkdir %REDIST_DIR%

echo ========================================
echo Building WebAssembly OSG Interface Tests...
echo ========================================
cd %BUILD_DIR%

echo Configuring CMake for WebAssembly...
emcmake cmake .. ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_CXX_STANDARD=20 ^
    -DCMAKE_PREFIX_PATH=%WASM_DEP_ROOT% ^
    -DCMAKE_FIND_ROOT_PATH=%WASM_DEP_ROOT% ^
    -DOSG_DIR=%WASM_DEP_ROOT%\lib\cmake\OpenSceneGraph ^
    -DOSGEARTH_DIR=%WASM_DEP_ROOT%\lib\cmake\osgEarth

if %ERRORLEVEL% neq 0 (
    echo WebAssembly CMake configuration failed!
    cd ..\..\..
    pause
    exit /b 1
)

echo Building WebAssembly OSG Interface Tests...
emmake make -j4

if %ERRORLEVEL% neq 0 (
    echo WebAssembly build failed!
    cd ..\..\..
    pause
    exit /b 1
)

echo ========================================
echo Copying WebAssembly files to redist...
echo ========================================
cd ..\..\..
copy "%BUILD_DIR%\osg_compatibility_test.html" "%REDIST_DIR%\" 2>nul
copy "%BUILD_DIR%\osg_compatibility_test.js" "%REDIST_DIR%\" 2>nul
copy "%BUILD_DIR%\osg_compatibility_test.wasm" "%REDIST_DIR%\" 2>nul
copy "%BUILD_DIR%\osg_compatibility_test.data" "%REDIST_DIR%\" 2>nul

echo ========================================
echo Creating proxy configuration file...
echo ========================================
echo ^{> "%REDIST_DIR%\proxy_config.json"
echo   "proxy": {>> "%REDIST_DIR%\proxy_config.json"
echo     "http": "http://127.0.0.1:10809",>> "%REDIST_DIR%\proxy_config.json"
echo     "https": "http://127.0.0.1:10809">> "%REDIST_DIR%\proxy_config.json"
echo   },>> "%REDIST_DIR%\proxy_config.json"
echo   "note": "Configure your browser to use this proxy for tile downloads">> "%REDIST_DIR%\proxy_config.json"
echo ^}>> "%REDIST_DIR%\proxy_config.json"

echo ========================================
echo Creating test runner script...
echo ========================================
echo @echo off> "%REDIST_DIR%\run_wasm_tests.bat"
echo echo Starting HTTP server for WebAssembly tests...>> "%REDIST_DIR%\run_wasm_tests.bat"
echo echo.>> "%REDIST_DIR%\run_wasm_tests.bat"
echo echo IMPORTANT: Configure your browser proxy to 127.0.0.1:10809>> "%REDIST_DIR%\run_wasm_tests.bat"
echo echo for downloading map tiles from Google Maps.>> "%REDIST_DIR%\run_wasm_tests.bat"
echo echo.>> "%REDIST_DIR%\run_wasm_tests.bat"
echo echo Starting server on http://localhost:8080>> "%REDIST_DIR%\run_wasm_tests.bat"
echo echo Open http://localhost:8080/osg_compatibility_test.html in your browser>> "%REDIST_DIR%\run_wasm_tests.bat"
echo echo.>> "%REDIST_DIR%\run_wasm_tests.bat"
echo python -m http.server 8080>> "%REDIST_DIR%\run_wasm_tests.bat"

echo ========================================
echo WebAssembly Build Complete!
echo ========================================
echo Build directory: %BUILD_DIR%
echo Redist directory: %REDIST_DIR%
echo.
echo To run WebAssembly tests:
echo 1. Configure browser proxy to 127.0.0.1:10809
echo 2. Run: cd %REDIST_DIR% ^&^& run_wasm_tests.bat
echo 3. Open http://localhost:8080/osg_compatibility_test.html
echo.
echo Available files:
dir /b "%REDIST_DIR%\*.*"
pause
