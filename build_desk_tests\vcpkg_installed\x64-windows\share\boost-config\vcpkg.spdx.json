{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/boost-config-x64-windows-1.88.0-cd07ed68-e72b-4618-946d-7bbcf1122675", "name": "boost-config:x64-windows@1.88.0 369e403895802866afb8e38ef41f0f0c364f350286e63a275ef9c7a3203750f6", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-14T04:45:10Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "boost-config", "SPDXID": "SPDXRef-port", "versionInfo": "1.88.0", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/boost-config", "homepage": "https://www.boost.org/libs/config", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Boost config module", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "boost-config:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "369e403895802866afb8e38ef41f0f0c364f350286e63a275ef9c7a3203750f6", "downloadLocation": "NONE", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "boostorg/config", "downloadLocation": "git+https://github.com/boostorg/config@boost-1.88.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "452a60074e33ee44dd193f34421e84c2bb63453b1fd9481b735f2b536cf941a7651faa29b0c74eed14d0a8e4021c0ad87f76575cb5180a8d897883579b68b69d"}]}], "files": [{"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "7f00f83e3e25dbf33eff48ebff137b52edf14ae771f6ef23586b4444c6e17b26"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "4d5e2955aa4658f96e00de21d6ca834773ba9432aacddf1a2da1ca22969ff8c7"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}