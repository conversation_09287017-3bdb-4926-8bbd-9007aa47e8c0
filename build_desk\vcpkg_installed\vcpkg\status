Package: vcpkg-cmake-config
Version: 2024-05-23
Architecture: x64-windows
Multi-Arch: same
Abi: 8d261494f5cab6cecd7851edc7eb14f27f3c82cd259e8a56095998d1199973a0
Status: install ok installed

Package: vcpkg-cmake
Version: 2024-04-23
Architecture: x64-windows
Multi-Arch: same
Abi: fa0a421976433b6b0635f7ccfd6e08f02a20aef877e749d6792bc1d9ae8233af
Status: install ok installed

Package: asmjit
Version: 2025-01-22
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 2cffcc6c8fab9b1d3faaea92dea5086fe0a552f7d25f069f0caf2abae398f78e
Description: AsmJit is a lightweight library for machine code generation written in C++ language
Status: install ok installed

Package: blend2d
Version: 2025-03-08
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 93f859bd55b40557c1820a2d6d1ba2a87c4265b01134bc91e09f3d94073e2def
Description: 2D Vector Graphics Engine Powered by a JIT Compiler
Default-Features: jit
Status: install ok installed

Package: blend2d
Feature: jit
Depends: asmjit
Architecture: x64-windows
Multi-Arch: same
Description: Enables JIT compiler to generate optimized pipelines.
Status: install ok installed

Package: draco
Version: 1.5.7
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 24899da6f9418adc36c917b2e372ea244bb922b6bddb818422158182ad377eea
Description: A library for compressing and decompressing 3D geometric meshes and point clouds. It is intended to improve the storage and transmission of 3D graphics.
Status: install ok installed

Package: geographiclib
Version: 2.5
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 624930fd90ae99702ef1706f3af454fddb6191d9115a1266ca2df43ef69a664b
Description: GeographicLib, a C++ library for performing geographic conversions
Status: install ok installed

Package: geos
Version: 3.13.0
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: c7446fcbaa65dcda4fdcba310e0ff175f25cf7f4b30dbf5a640aec2898dec309
Description: Geometry Engine Open Source
Status: install ok installed

Package: libwebp
Version: 1.5.0
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 9dea8edbb6f50d354f612dbadf7296d2adf13758e99411f919e0bbe8ae495da8
Description: WebP codec: library to encode and decode images in WebP format
Default-Features: libwebpmux, nearlossless, simd
Status: install ok installed

Package: libwebp
Feature: libwebpmux
Architecture: x64-windows
Multi-Arch: same
Description: Build the libwebpmux library
Status: install ok installed

Package: libwebp
Feature: nearlossless
Architecture: x64-windows
Multi-Arch: same
Description: Enable near-lossless encoding
Status: install ok installed

Package: libwebp
Feature: simd
Architecture: x64-windows
Multi-Arch: same
Description: Enable any SIMD optimization.
Status: install ok installed

Package: libwebp
Feature: unicode
Architecture: x64-windows
Multi-Arch: same
Description: Build Unicode executables. (Adds definition UNICODE and _UNICODE)
Status: install ok installed

Package: zlib
Version: 1.3.1
Depends: vcpkg-cmake
Architecture: x64-windows
Multi-Arch: same
Abi: 435984fbeb96ef7889ac851a04da6ae2211492d5d4d055b103e09923536ad2d8
Description: A compression library
Status: install ok installed

Package: bzip2
Version: 1.0.8
Port-Version: 6
Depends: vcpkg-cmake
Architecture: x64-windows
Multi-Arch: same
Abi: e6a1d2088f7f4359cf05733e9cd5da5602eb60ff1083444aca8c69f55f09b46d
Description: bzip2 is a freely available, patent free, high-quality data compressor. It typically compresses files to within 10% to 15% of the best available techniques (the PPM family of statistical compressors), whilst being around twice as fast at compression and six times faster at decompression.
Default-Features: tool
Status: install ok installed

Package: bzip2
Feature: tool
Architecture: x64-windows
Multi-Arch: same
Description: Builds bzip2 executable
Status: install ok installed

Package: libzip
Version: 1.11.4
Depends: vcpkg-cmake, vcpkg-cmake-config, zlib
Architecture: x64-windows
Multi-Arch: same
Abi: e11ad2c3df67648c627127a8b3a69a83dd3af5db04b331d81629119924a66cb6
Description: A C library for reading, creating, and modifying zip archives.
Default-Features: bzip2, default-aes
Status: install ok installed

Package: libzip
Feature: bzip2
Depends: bzip2
Architecture: x64-windows
Multi-Arch: same
Description: Support bzip2-compressed zip archives
Status: install ok installed

Package: libzip
Feature: default-aes
Architecture: x64-windows
Multi-Arch: same
Description: AES (encryption) support using a default backend
Status: install ok installed

Package: meshoptimizer
Version: 0.24
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 877df2fa7d4974a92c62be8caf751416e1e5b995d840b59659839285cdeabb40
Description: Mesh optimization library that makes meshes smaller and faster to render
Status: install ok installed

Package: egl-registry
Version: 2024-01-25
Architecture: x64-windows
Multi-Arch: same
Abi: 8a6fd9128814abecc01108e40172468def07b63604dc8987316b371209b1a33c
Description: EGL API and Extension Registry
Status: install ok installed

Package: opengl-registry
Version: 2024-02-10
Port-Version: 1
Depends: egl-registry
Architecture: x64-windows
Multi-Arch: same
Abi: 53bed305119e66685dfeb18c7889d295c606c20795809b9cbe4c7d255aaacae2
Description: OpenGL, OpenGL ES, and OpenGL ES-SC API and Extension Registry
Status: install ok installed

Package: opengl
Version: 2022-12-04
Port-Version: 3
Depends: opengl-registry
Architecture: x64-windows
Multi-Arch: same
Abi: 28a23cfcb002997df5dcde1f54a7110ebf7ca2a4baa39a673da0951098a92f53
Description: Open Graphics Library (OpenGL)[3][4][5] is a cross-language, cross-platform application programming interface (API) for rendering 2D and 3D vector graphics.
Status: install ok installed

Package: liblzma
Version: 5.8.1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: e7f2ac12b18b7696dff8dd54fb3f50093d7f6e7a5846d7d7555ad1a1b8e8e08f
Description: Compression library with an API similar to that of zlib.
Status: install ok installed

Package: libjpeg-turbo
Version: 3.1.0
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 296ca75fcb1eae1dff46c32a7821fd5c366312e98bae969dc521098daf59a53e
Description: libjpeg-turbo is a JPEG image codec that uses SIMD instructions (MMX, SSE2, NEON, AltiVec) to accelerate baseline JPEG compression and decompression on x86, x86-64, ARM, and PowerPC systems.
Status: install ok installed

Package: tiff
Version: 4.7.0
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 0e53014587649b3b8a7371efe32feeedf1d848ae7fe6bbec6afad7d403399c6c
Description: A library that supports the manipulation of TIFF image files
Default-Features: jpeg, lzma, zip
Status: install ok installed

Package: tiff
Feature: jpeg
Depends: libjpeg-turbo
Architecture: x64-windows
Multi-Arch: same
Description: Support JPEG compression in TIFF image files
Status: install ok installed

Package: tiff
Feature: lzma
Depends: liblzma
Architecture: x64-windows
Multi-Arch: same
Description: Support LZMA compression in TIFF image files
Status: install ok installed

Package: tiff
Feature: zip
Depends: zlib
Architecture: x64-windows
Multi-Arch: same
Description: Support ZIP/deflate compression in TIFF image files
Status: install ok installed

Package: libiconv
Version: 1.18
Port-Version: 1
Architecture: x64-windows
Multi-Arch: same
Abi: fe5e084019e95af8b3b1464c9f6989610f091d64a4dd5490eae460f78a836453
Description: iconv() text conversion.
    This port installs GNU libiconv if the system C runtime doesn't provide a suitable iconv() implementation.
Status: install ok installed

Package: libxml2
Version: 2.13.8
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: f369615d5cc2bff72aa2e3c5a0bca89a0fd13c7d09f9e740b1141158c30c5831
Description: Libxml2 is the XML C parser and toolkit developed for the Gnome project (but usable outside of the Gnome platform).
Default-Features: iconv, zlib
Status: install ok installed

Package: libxml2
Feature: http
Architecture: x64-windows
Multi-Arch: same
Description: Add the HTTP support
Status: install ok installed

Package: libxml2
Feature: iconv
Depends: libiconv
Architecture: x64-windows
Multi-Arch: same
Description: Add ICONV support
Status: install ok installed

Package: libxml2
Feature: zlib
Depends: zlib
Architecture: x64-windows
Multi-Arch: same
Description: Use ZLib
Status: install ok installed

Package: libpng
Version: 1.6.48
Depends: vcpkg-cmake, vcpkg-cmake-config, zlib
Architecture: x64-windows
Multi-Arch: same
Abi: eb802785482d290c987b807233ec7a89a4cc2feafc8898f132e9673a07be073d
Description: libpng is a library implementing an interface for reading and writing PNG (Portable Network Graphics) format files
Status: install ok installed

Package: libgta
Version: 1.0.8
Port-Version: 5
Depends: bzip2, liblzma, vcpkg-cmake, zlib
Architecture: x64-windows
Multi-Arch: same
Abi: b98644cae0fe1a3a4f001eb8f2439f63df2773369974c687d2998082549634c2
Description: Libgta is a portable library that implements the Generic Tagged Array (GTA) file format.
Status: install ok installed

Package: jasper
Version: 4.2.4
Port-Version: 2
Depends: libjpeg-turbo, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: ae0619b30c016329ce32a398d37fb18b17147b37fe782428edb77f2edf3429fc
Description: Open source implementation of the JPEG-2000 Part-1 standard
Status: install ok installed

Package: giflib
Version: 5.2.2
Port-Version: 2
Depends: vcpkg-cmake
Architecture: x64-windows
Multi-Arch: same
Abi: 1957d789238348081a1a5d4a9256057c245788464f2c2a6b87c41f8dfb7abe56
Description: A library for reading and writing gif images.
Status: install ok installed

Package: zstd
Version: 1.5.7
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 64321c99b54901238b06be3e54761aa6b151e0350525b7dc804dd04a2b4a354a
Description: Zstandard - Fast real-time compression algorithm
Status: install ok installed

Package: sqlite3
Version: 3.50.2
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 10e4b961eaf0e8e4f81e38d249eda3055c6ab374b4499b027074f64b17cda66b
Description: SQLite is a software library that implements a self-contained, serverless, zero-configuration, transactional SQL database engine.
Default-Features: json1
Status: install ok installed

Package: sqlite3
Feature: json1
Architecture: x64-windows
Multi-Arch: same
Description: Enable JSON functionality for sqlite3
Status: install ok installed

Package: sqlite3
Feature: rtree
Architecture: x64-windows
Multi-Arch: same
Description: Enable the RTREE extension
Status: install ok installed

Package: sqlite3
Feature: tool
Architecture: x64-windows
Multi-Arch: same
Description: Build sqlite3 executable
Status: install ok installed

Package: qhull
Version: 8.0.2
Port-Version: 5
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: bbd6d67180baad38314d0a1e95d8ea7602d9d448a0e2ae39f9b835da762b1d0a
Description: computes the convex hull, Delaunay triangulation, Voronoi diagram
Status: install ok installed

Package: vcpkg-cmake-get-vars
Version: 2025-05-29
Depends: vcpkg-cmake
Architecture: x64-windows
Multi-Arch: same
Abi: 5b9c6c71e45a0caaecd95d58aa8ebb05fdf46c1f4e2d8eb838ae0730e64bb7e4
Status: install ok installed

Package: openssl
Version: 3.5.0
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config, vcpkg-cmake-get-vars
Architecture: x64-windows
Multi-Arch: same
Abi: a6620427596fa5463ff22f490a7b0615924bf9a036dcaf2198fdcbedcd2e9623
Description: OpenSSL is an open source project that provides a robust, commercial-grade, and full-featured toolkit for the Transport Layer Security (TLS) and Secure Sockets Layer (SSL) protocols. It is also a general-purpose cryptography library.
Status: install ok installed

Package: lz4
Version: 1.10.0
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 4965ddaf92ee7c49bd869b6a494612e9c9da6b4676a85d836ea3244b8c37c6f3
Description: Lossless compression algorithm, providing compression speed at 400 MB/s per core.
Status: install ok installed

Package: vcpkg-tool-meson
Version: 1.8.2
Depends: vcpkg-cmake-get-vars
Architecture: x64-windows
Multi-Arch: same
Abi: e5bf6cfafcbfa4261cf4b2519ff9531db17cb91bb1e0bc62088c48e5e5e5afea
Description: Meson build system
Status: install ok installed

Package: pkgconf
Version: 2.4.3
Port-Version: 1
Depends: vcpkg-tool-meson
Architecture: x64-windows
Multi-Arch: same
Abi: d2de46e042cc0bdbbae7e3adcd3a38ca57d2b188cca2d86bd998c911374fad31
Description: pkgconf is a program which helps to configure compiler and linker flags for development libraries. It is similar to pkg-config from freedesktop.org.
Status: install ok installed

Package: vcpkg-pkgconfig-get-modules
Version: 2024-04-03
Depends: pkgconf
Architecture: x64-windows
Multi-Arch: same
Abi: c349fed9d0cb2df9a88b897024146b347ae36970a7430627b78e4c0a2c705e7e
Status: install ok installed

Package: libpq
Version: 16.9
Depends: vcpkg-cmake-get-vars, vcpkg-pkgconfig-get-modules
Architecture: x64-windows
Multi-Arch: same
Abi: 27751daea912ee407b1de16eb738777064900c67f26bef5d31354e8a270f0b2f
Description: The official database access API of postgresql
Default-Features: lz4, openssl, zlib
Status: install ok installed

Package: libpq
Feature: lz4
Depends: lz4
Architecture: x64-windows
Multi-Arch: same
Description: Use lz4
Status: install ok installed

Package: libpq
Feature: openssl
Depends: openssl
Architecture: x64-windows
Multi-Arch: same
Description: support for encrypted client connections and random number generation on platforms that do not have "/dev/urandom" (except windows)
Status: install ok installed

Package: libpq
Feature: zlib
Depends: zlib
Architecture: x64-windows
Multi-Arch: same
Description: Use zlib
Status: install ok installed

Package: pcre2
Version: 10.45
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 407984dacc0fe2a777254383e3f6067a50ad60fcf35611d2d854e7705d29d762
Description: Regular Expression pattern matching using the same syntax and semantics as Perl 5.
Default-Features: platform-default-features
Status: install ok installed

Package: pcre2
Feature: jit
Architecture: x64-windows
Multi-Arch: same
Description: Enable support for Just-In-Time compiling regex matchers
Status: install ok installed

Package: pcre2
Feature: platform-default-features
Architecture: x64-windows
Multi-Arch: same
Description: Enable default features
Status: install ok installed

Package: openjpeg
Version: 2.5.3
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: b32685d866b2fed0456d66ebeaee85aa2a07091eb0e48168402a14a0c1fede83
Description: OpenJPEG is an open-source JPEG 2000 codec written in C language. It has been developed in order to promote the use of JPEG 2000, a still-image compression standard from the Joint Photographic Experts Group (JPEG). Since April 2015, it is officially recognized by ISO/IEC and ITU-T as a JPEG 2000 Reference Software.
Status: install ok installed

Package: curl
Version: 8.14.1
Depends: vcpkg-cmake, vcpkg-cmake-config, zlib
Architecture: x64-windows
Multi-Arch: same
Abi: 6e318470fab613f7470c8c523682777365a86a2be21fabe01719c9c79a268559
Description: A library for transferring data with URLs
Default-Features: non-http, ssl
Status: install ok installed

Package: curl
Feature: non-http
Architecture: x64-windows
Multi-Arch: same
Description: Enables protocols beyond HTTP/HTTPS/HTTP2
Status: install ok installed

Package: curl
Feature: schannel
Architecture: x64-windows
Multi-Arch: same
Description: SSL support (Secure Channel)
Status: install ok installed

Package: curl
Feature: ssl
Architecture: x64-windows
Multi-Arch: same
Description: Default SSL backend
Status: install ok installed

Package: curl
Feature: sspi
Architecture: x64-windows
Multi-Arch: same
Description: SSPI support
Status: install ok installed

Package: libaec
Version: 1.1.3
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 8a0c98960159372d56ba90e13e0a642986aeae449c14550d3c57349ec11b6f1f
Description: Adaptive Entropy Coding library
Status: install ok installed

Package: hdf5
Version: 1.14.6
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 5e90c6695a90a306434ab9efbbb5f926ed18593a4d66703d65db96be40eb162f
Description: HDF5 is a data model, library, and file format for storing and managing data
Default-Features: szip, zlib
Status: install ok installed

Package: hdf5
Feature: cpp
Architecture: x64-windows
Multi-Arch: same
Description: Builds cpp lib
Status: install ok installed

Package: hdf5
Feature: szip
Depends: libaec
Architecture: x64-windows
Multi-Arch: same
Description: Build with szip
Status: install ok installed

Package: hdf5
Feature: zlib
Depends: zlib
Architecture: x64-windows
Multi-Arch: same
Description: Build with zlib
Status: install ok installed

Package: netcdf-c
Version: 4.8.1
Port-Version: 6
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 4b54c1e87e9a4c4535be0409b11498be9e6f0215ba4b46cdaa73bbf3f6517495
Description: A set of self-describing, machine-independent data formats that support the creation, access, and sharing of array-oriented scientific data.
Default-Features: dap, hdf5, nczarr, netcdf-4
Status: install ok installed

Package: netcdf-c
Feature: dap
Depends: curl
Architecture: x64-windows
Multi-Arch: same
Description: Build with DAP remote access client support
Status: install ok installed

Package: netcdf-c
Feature: hdf5
Depends: hdf5, vcpkg-pkgconfig-get-modules
Architecture: x64-windows
Multi-Arch: same
Description: Build with HDF5 support
Status: install ok installed

Package: netcdf-c
Feature: nczarr
Depends: curl
Architecture: x64-windows
Multi-Arch: same
Description: Build with NCZarr cloud storage access support
Status: install ok installed

Package: netcdf-c
Feature: netcdf-4
Architecture: x64-windows
Multi-Arch: same
Description: Build with netCDF-4 support
Status: install ok installed

Package: minizip
Version: 1.3.1
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config, vcpkg-cmake-get-vars, zlib
Architecture: x64-windows
Multi-Arch: same
Abi: 09250874e9312e41a0b5ffbcfbc60296faf5074a355bbfd9bf0d13ed31355b83
Description: Minizip zip file manipulation library
Status: install ok installed

Package: expat
Version: 2.7.1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: b6c4773eb26acc1cc35237a03d8a2dfaab71596f08ff5ae5b7ee9d124baf02c8
Description: XML parser library written in C
Status: install ok installed

Package: freexl
Version: 2.0.0
Port-Version: 1
Depends: expat, libiconv, minizip
Architecture: x64-windows
Multi-Arch: same
Abi: 73547531cdf129eae2d6303907040976ecc213bbb274d61df3dff401307ec357
Description: FreeXL is an open source library to extract valid data from within an Excel (.xls) spreadsheet
Status: install ok installed

Package: nlohmann-json
Version: 3.12.0
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: c4a859510564d1a1833a7fb3e7315cfad11f3db013b1dd215af6da8714a34cc9
Description: JSON for Modern C++
Status: install ok installed

Package: proj
Version: 9.6.2
Depends: nlohmann-json, sqlite3, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: f7c8fd0d70aadf30ba0a0377326ffa204a7a7fe2fe634e58c121c2d49344fb24
Description: PROJ library for cartographic projections
Default-Features: net, tiff
Status: install ok installed

Package: proj
Feature: net
Depends: curl
Architecture: x64-windows
Multi-Arch: same
Description: Enable network support
Status: install ok installed

Package: proj
Feature: tiff
Depends: tiff
Architecture: x64-windows
Multi-Arch: same
Description: Enable TIFF support to read some grids
Status: install ok installed

Package: libspatialite
Version: 5.1.0
Port-Version: 4
Depends: geos, libiconv, libxml2, proj, sqlite3, vcpkg-pkgconfig-get-modules, zlib
Architecture: x64-windows
Multi-Arch: same
Abi: c7c8cac5d490ad73466c3e43c8a23bab7ea35b7a5930d139e883298dcb4b088c
Description: SpatiaLite is an open source library intended to extend the SQLite core to support fully fledged Spatial SQL capabilities.
Default-Features: freexl
Status: install ok installed

Package: libspatialite
Feature: freexl
Depends: freexl
Architecture: x64-windows
Multi-Arch: same
Description: FreeXL spreadsheet file support.
Status: install ok installed

Package: uriparser
Version: 0.9.8
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: b9118cd2ea35bf53e65d07eb03e24a5a48869d2914caac39dbc6ae8b0f305f91
Description: Strictly RFC 3986 compliant URI parsing and handling library written in C89.
Status: install ok installed

Package: vcpkg-boost
Version: 2025-03-29
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 3799f489a271c94b6d4483ca1ca4e8d68546f47af7ad32a4932ac661389690ba
Status: install ok installed

Package: boost-uninstall
Version: 1.88.0
Architecture: x64-windows
Multi-Arch: same
Abi: c7cd9e736dc7801e39652bfbd6d276d49ae02248a80c1c6c765957578c0e66c3
Description: Internal vcpkg port used to uninstall Boost
Status: install ok installed

Package: boost-cmake
Version: 1.88.0
Depends: boost-uninstall, vcpkg-boost
Architecture: x64-windows
Multi-Arch: same
Abi: 52b610516189e7521f325eb4b1d275c8c28713a2f6d70d7df96c3ac9ceb81c0d
Description: Boost cmake module
Status: install ok installed

Package: boost-headers
Version: 1.88.0
Depends: boost-cmake
Architecture: x64-windows
Multi-Arch: same
Abi: 51c99302e0898e4b5c11bb8f92c85f6c1dddf53c1d4be75ecb61fa56d9e252f8
Description: Boost headers module
Status: install ok installed

Package: boost-config
Version: 1.88.0
Depends: boost-cmake, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: a946832722e60706145d03f8e1e99c7d84ae413f72b648ac6c40abd12f49bc40
Description: Boost config module
Status: install ok installed

Package: boost-assert
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: a187c96e497f34169727d3e79d863318e5edbb34644aa285b8da02b55b9cf0ca
Description: Boost assert module
Status: install ok installed

Package: boost-throw-exception
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: ed8d0de07a56d296efa6e0e41ad86c3f031c2aa6e6e8d9cdbd6f5bb8ba7d2321
Description: Boost throw_exception module
Status: install ok installed

Package: boost-static-assert
Version: 1.88.0
Depends: boost-cmake, boost-config, boost-headers
Architecture: x64-windows
Multi-Arch: same
Abi: 7ddb0cffed77aafab0fe483d3dda4c9ee038680f6f01bf42822738b5f0a1e67b
Description: Boost static_assert module
Status: install ok installed

Package: boost-core
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-headers, boost-static-assert, boost-throw-exception
Architecture: x64-windows
Multi-Arch: same
Abi: 9eef14152bfa55a2552b66be9fdc6f2bdbc9401f387351c55fc82923c873aadd
Description: Boost core module
Status: install ok installed

Package: boost-smart-ptr
Version: 1.88.0
Depends: boost-assert, boost-cmake, boost-config, boost-core, boost-headers, boost-throw-exception
Architecture: x64-windows
Multi-Arch: same
Abi: 85ffdf4549506ab90faebfb13984db95353f0b2b0950a14f65adc9c05b61e9ae
Description: Boost smart_ptr module
Status: install ok installed

Package: libkml
Version: 1.3.0
Port-Version: 13
Depends: boost-smart-ptr, expat, minizip, uriparser, vcpkg-cmake, vcpkg-cmake-config, zlib
Architecture: x64-windows
Multi-Arch: same
Abi: 491e9b343788fdadbd2e604607f8ccef581f51c09732c8dfdd977830f8889c31
Description: Reference implementation of OGC KML 2.2
Status: install ok installed

Package: lerc
Version: 4.0.4
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 84c2671f2dc0318d10366be4fd97255f6d09e9613b1af036542ff581924930ce
Description: An open-source image or raster format which supports rapid encoding and decoding for any pixel type
Status: install ok installed

Package: libgeotiff
Version: 1.7.4
Depends: proj, tiff, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 6ffba80938929d9e3796c92e2ecf45443d73dc223ed7b555748bced1ccd38963
Description: Libgeotiff is an open source library on top of libtiff for reading and writing GeoTIFF information tags.
Status: install ok installed

Package: json-c
Version: 0.18-20240915
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 3fbb223e664052d491a9d79482552debded650cd881458bf94e581ab11c9d99d
Description: A JSON implementation in C
Status: install ok installed

Package: gdal
Version: 3.11.0
Port-Version: 1
Depends: json-c, libgeotiff, pkgconf, proj, tiff, vcpkg-cmake, vcpkg-cmake-config, vcpkg-pkgconfig-get-modules, zlib
Architecture: x64-windows
Multi-Arch: same
Abi: d81ef888f6d707ddc3169ba516f72f7df85087bbc4161dc5ff04634efd7c4cdb
Description: The Geographic Data Abstraction Library for reading and writing geospatial raster and vector data
Default-Features: gif, hdf5, iconv, libkml, libspatialite, libxml2, lzma, netcdf, openjpeg, openssl, pcre2, postgresql, recommended-features, webp, zstd
Status: install ok installed

Package: gdal
Feature: curl
Depends: curl
Architecture: x64-windows
Multi-Arch: same
Description: Enable CURL network support
Status: install ok installed

Package: gdal
Feature: expat
Depends: expat
Architecture: x64-windows
Multi-Arch: same
Description: Use EXPAT library
Status: install ok installed

Package: gdal
Feature: geos
Depends: geos
Architecture: x64-windows
Multi-Arch: same
Description: Enable GEOS support
Status: install ok installed

Package: gdal
Feature: gif
Depends: giflib
Architecture: x64-windows
Multi-Arch: same
Description: Enable GIF support
Status: install ok installed

Package: gdal
Feature: hdf5
Depends: hdf5
Architecture: x64-windows
Multi-Arch: same
Description: Enable HDF5 support
Status: install ok installed

Package: gdal
Feature: iconv
Depends: libiconv
Architecture: x64-windows
Multi-Arch: same
Description: Use iconv library
Status: install ok installed

Package: gdal
Feature: jpeg
Depends: libjpeg-turbo
Architecture: x64-windows
Multi-Arch: same
Description: Use JPEG compression library
Status: install ok installed

Package: gdal
Feature: lerc
Depends: lerc
Architecture: x64-windows
Multi-Arch: same
Description: Enable LERC support
Status: install ok installed

Package: gdal
Feature: libkml
Depends: libkml
Architecture: x64-windows
Multi-Arch: same
Description: Enable the LibKML driver
Status: install ok installed

Package: gdal
Feature: libspatialite
Depends: libspatialite
Architecture: x64-windows
Multi-Arch: same
Description: Create or update SpatiaLite databases using libspatialite
Status: install ok installed

Package: gdal
Feature: libxml2
Depends: libxml2
Architecture: x64-windows
Multi-Arch: same
Description: Use LibXML2 library
Status: install ok installed

Package: gdal
Feature: lzma
Depends: liblzma
Architecture: x64-windows
Multi-Arch: same
Description: Use LZMA library
Status: install ok installed

Package: gdal
Feature: netcdf
Depends: netcdf-c
Architecture: x64-windows
Multi-Arch: same
Description: Enable NetCDF support
Status: install ok installed

Package: gdal
Feature: openjpeg
Depends: openjpeg
Architecture: x64-windows
Multi-Arch: same
Description: Use OpenJPEG library
Status: install ok installed

Package: gdal
Feature: openssl
Depends: openssl
Architecture: x64-windows
Multi-Arch: same
Description: Use OpenSSL library
Status: install ok installed

Package: gdal
Feature: pcre2
Depends: pcre2
Architecture: x64-windows
Multi-Arch: same
Description: Enable PCRE2 support for sqlite3
Status: install ok installed

Package: gdal
Feature: png
Depends: libpng
Architecture: x64-windows
Multi-Arch: same
Description: Use PNG compression library
Status: install ok installed

Package: gdal
Feature: postgresql
Depends: libpq
Architecture: x64-windows
Multi-Arch: same
Description: Enable PostgreSQL support
Status: install ok installed

Package: gdal
Feature: qhull
Depends: qhull
Architecture: x64-windows
Multi-Arch: same
Description: Use QHULL library
Status: install ok installed

Package: gdal
Feature: recommended-features
Architecture: x64-windows
Multi-Arch: same
Description: Features that are explicity marked as recommended by GDAL.
Status: install ok installed

Package: gdal
Feature: sqlite3
Depends: sqlite3
Architecture: x64-windows
Multi-Arch: same
Description: Enable SQLite3 support
Status: install ok installed

Package: gdal
Feature: webp
Depends: libwebp
Architecture: x64-windows
Multi-Arch: same
Description: Enable WEBP support
Status: install ok installed

Package: gdal
Feature: zstd
Depends: zstd
Architecture: x64-windows
Multi-Arch: same
Description: Use ZSTD library
Status: install ok installed

Package: libdeflate
Version: 1.24
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 29bc9ffabfae7c1726690c4f97e2b4056d6cdf1fdf9659662e264d4acd2dae7a
Description: libdeflate is a library for fast, whole-buffer DEFLATE-based compression and decompression.
Default-Features: compression, decompression, gzip, zlib
Status: install ok installed

Package: libdeflate
Feature: compression
Architecture: x64-windows
Multi-Arch: same
Description: Support compression
Status: install ok installed

Package: libdeflate
Feature: decompression
Architecture: x64-windows
Multi-Arch: same
Description: Support decompression
Status: install ok installed

Package: libdeflate
Feature: gzip
Architecture: x64-windows
Multi-Arch: same
Description: Support the gzip format
Status: install ok installed

Package: libdeflate
Feature: zlib
Architecture: x64-windows
Multi-Arch: same
Description: Support the zlib format
Status: install ok installed

Package: imath
Version: 3.1.12
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 7099e14af5b52b5a317523067d47cee0ac3f39902db5b8afa21a2d0709314b7c
Description: Imath is a C++ and Python library of 2D and 3D vector, matrix, and math operations for computer graphics.
Status: install ok installed

Package: openexr
Version: 3.3.4
Depends: imath, libdeflate, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: e1c13b9a0369a41685fda51203ea34c8723d0a698382cef00166df81b9a4ae8b
Description: OpenEXR is a high dynamic-range (HDR) image file format developed by Industrial Light & Magic for use in computer imaging applications
Status: install ok installed

Package: libsquish
Version: 1.15
Port-Version: 14
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 30d3400eecd1537a06b612aaf7434dc84c048c3598827182661831dc9a0cc8ee
Description: Open source DXT compression library.
Status: install ok installed

Package: nvtt
Version: 2.1.2
Port-Version: 9
Depends: libsquish, vcpkg-cmake
Architecture: x64-windows
Multi-Arch: same
Abi: b606fa2c25ac50831a3f2def37ac7537c72aa7df614022f3d101bba96b97990e
Description: Texture processing tools with support for Direct3D 10 and 11 formats.
Status: install ok installed

Package: brotli
Version: 1.1.0
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: ea8d09d63ff0a6d3fa09cfb577c7c102078a7d15c39770595e1bda9421b58d88
Description: a generic-purpose lossless compression algorithm that compresses data using a combination of a modern variant of the LZ77 algorithm, Huffman coding and 2nd order context modeling.
Status: install ok installed

Package: freetype
Version: 2.13.3
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: d567993976f4cb44c08b2e5a53c829754d97209871617290641b45af1aafaade
Description: A library to render fonts.
Default-Features: brotli, bzip2, png, zlib
Status: install ok installed

Package: freetype
Feature: brotli
Depends: brotli
Architecture: x64-windows
Multi-Arch: same
Description: Support decompression of WOFF2 streams
Status: install ok installed

Package: freetype
Feature: bzip2
Depends: bzip2
Architecture: x64-windows
Multi-Arch: same
Description: Support bzip2 compressed fonts.
Status: install ok installed

Package: freetype
Feature: png
Depends: libpng
Architecture: x64-windows
Multi-Arch: same
Description: Support PNG compressed OpenType embedded bitmaps.
Status: install ok installed

Package: freetype
Feature: zlib
Depends: zlib
Architecture: x64-windows
Multi-Arch: same
Description: Use zlib instead of internal library for DEFLATE
Status: install ok installed

Package: gperf
Version: 3.1
Port-Version: 7
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 0a5be1dcffad46fe06eab1a20813ca6f9be3d3c081b89e8aabbd9d91b4b979e7
Description: GNU perfect hash function generator
Status: install ok installed

Package: dirent
Version: 1.25
Architecture: x64-windows
Multi-Arch: same
Abi: ce38e5f3ef1e0ef32c887479c6fec047d1fc7d7425610b834d518e1ce5bb6807
Description: Dirent is a C/C++ programming interface that allows programmers to retrieve information about files and directories under Linux/UNIX. This project provides Linux compatible Dirent interface for Microsoft Windows.
Status: install ok installed

Package: fontconfig
Version: 2.15.0
Port-Version: 4
Depends: dirent, expat, freetype, gperf, vcpkg-tool-meson
Architecture: x64-windows
Multi-Arch: same
Abi: c4da362360cb0a0b0034baf01696b5cdbdd53c234579e8bcf0acc6b9aab723f3
Description: Library for configuring and customizing font access.
Status: install ok installed

Package: osg
Version: 3.6.5
Port-Version: 27
Depends: expat, opengl-registry, vcpkg-cmake, vcpkg-cmake-config, zlib
Architecture: x64-windows
Multi-Arch: same
Abi: 1c03c1d028925d5d1f7bf279083a0ef2649ad8fa32af7a5b32d252e3bd0203fc
Description: The OpenSceneGraph is an open source high performance 3D graphics toolkit.
Default-Features: fontconfig, freetype, nvtt, openexr, plugins
Status: install ok installed

Package: osg
Feature: fontconfig
Depends: fontconfig
Architecture: x64-windows
Multi-Arch: same
Description: Enable Fontconfig support for osgText
Status: install ok installed

Package: osg
Feature: freetype
Depends: freetype
Architecture: x64-windows
Multi-Arch: same
Description: Enable Freetype support
Status: install ok installed

Package: osg
Feature: nvtt
Depends: nvtt
Architecture: x64-windows
Multi-Arch: same
Description: Build texture processing tools plugin
Status: install ok installed

Package: osg
Feature: openexr
Depends: openexr
Architecture: x64-windows
Multi-Arch: same
Description: Build the exr plugin
Status: install ok installed

Package: osg
Feature: plugins
Depends: curl, gdal, giflib, jasper, libgta, libiconv, libjpeg-turbo, libpng, libxml2, tiff
Architecture: x64-windows
Multi-Arch: same
Description: Build most OSG plugins
Status: install ok installed

Package: abseil
Version: 20250127.1
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: b225ae237b72421de173efa16e683866ae439994354e9933049bbe88a2ab8280
Description: Abseil is an open-source collection of C++ library code designed to augment the C++ standard library. The Abseil library code is collected from Google's own C++ code base, has been extensively tested and used in production, and is the same code we depend on in our daily coding lives.
    In some cases, Abseil provides pieces missing from the C++ standard; in others, Abseil provides alternatives to the standard for special needs we've found through usage in the Google code base. We denote those cases clearly within the library code we provide you.
    Abseil is not meant to be a competitor to the standard library; we've just found that many of these utilities serve a purpose within our code base, and we now want to provide those resources to the C++ community as a whole.
Status: install ok installed

Package: utf8-range
Version: 5.29.3
Depends: abseil, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: c99e44f45d2e5d102d01b65ea31ede88d8984ce618f67e99d609d6003f2fa8d6
Description: Fast UTF-8 validation with Range algorithm (NEON+SSE4+AVX2)
Status: install ok installed

Package: protobuf
Version: 5.29.3
Port-Version: 1
Depends: abseil, utf8-range, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: ac9a013f2f4d8a4feb96030193a9e324d09b3c03dec5625b4a2c44761334d9b4
Description: Google's language-neutral, platform-neutral, extensible mechanism for serializing structured data.
Status: install ok installed

Package: rocksdb
Version: 10.2.1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: ad34f7162db64ef63504083454f5132e19a2a29f0a5203b51b9925924318d3f6
Description: A library that provides an embeddable, persistent key-value store for fast storage
Default-Features: zlib
Status: install ok installed

Package: rocksdb
Feature: zlib
Depends: zlib
Architecture: x64-windows
Multi-Arch: same
Description: build with zlib
Status: install ok installed

Package: fmt
Version: 11.0.2
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 424bd90e190980d37ab2b51ebbac4ceeb43fe95b50997d46ec42047418f53fbd
Description: {fmt} is an open-source formatting library providing a fast and safe alternative to C stdio and C++ iostreams.
Status: install ok installed

Package: spdlog
Version: 1.15.3
Depends: fmt, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 18dbe19575e5c2f8105d56cd473c62eabfb38ac5091f3289af23e9c485316681
Description: Very fast, header-only/compiled, C++ logging library.
Status: install ok installed

