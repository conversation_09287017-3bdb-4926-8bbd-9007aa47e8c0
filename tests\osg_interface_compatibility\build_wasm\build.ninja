# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.26

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: OSGInterfaceCompatibilityTests
# Configurations: Release
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Release
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_wasm/
# =============================================================================
# Object build statements for EXECUTABLE target osg_compatibility_test


#############################################
# Order-only phony target for osg_compatibility_test

build cmake_object_order_depends_target_osg_compatibility_test: phony || CMakeFiles/osg_compatibility_test.dir

build CMakeFiles/osg_compatibility_test.dir/OSGInterfaceTestFramework.cpp.o: CXX_COMPILER__osg_compatibility_test_unscanned_Release F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/OSGInterfaceTestFramework.cpp || cmake_object_order_depends_target_osg_compatibility_test
  DEFINES = -DGL_GLEXT_PROTOTYPES -D__EMSCRIPTEN__
  DEP_FILE = CMakeFiles\osg_compatibility_test.dir\OSGInterfaceTestFramework.cpp.o.d
  FLAGS = -O3 -DNDEBUG -std=gnu++20 -Wall -Wextra
  INCLUDES = -IF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include
  OBJECT_DIR = CMakeFiles\osg_compatibility_test.dir
  OBJECT_FILE_DIR = CMakeFiles\osg_compatibility_test.dir

build CMakeFiles/osg_compatibility_test.dir/CoreRenderingTests.cpp.o: CXX_COMPILER__osg_compatibility_test_unscanned_Release F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/CoreRenderingTests.cpp || cmake_object_order_depends_target_osg_compatibility_test
  DEFINES = -DGL_GLEXT_PROTOTYPES -D__EMSCRIPTEN__
  DEP_FILE = CMakeFiles\osg_compatibility_test.dir\CoreRenderingTests.cpp.o.d
  FLAGS = -O3 -DNDEBUG -std=gnu++20 -Wall -Wextra
  INCLUDES = -IF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include
  OBJECT_DIR = CMakeFiles\osg_compatibility_test.dir
  OBJECT_FILE_DIR = CMakeFiles\osg_compatibility_test.dir

build CMakeFiles/osg_compatibility_test.dir/main.cpp.o: CXX_COMPILER__osg_compatibility_test_unscanned_Release F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/main.cpp || cmake_object_order_depends_target_osg_compatibility_test
  DEFINES = -DGL_GLEXT_PROTOTYPES -D__EMSCRIPTEN__
  DEP_FILE = CMakeFiles\osg_compatibility_test.dir\main.cpp.o.d
  FLAGS = -O3 -DNDEBUG -std=gnu++20 -Wall -Wextra
  INCLUDES = -IF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include
  OBJECT_DIR = CMakeFiles\osg_compatibility_test.dir
  OBJECT_FILE_DIR = CMakeFiles\osg_compatibility_test.dir


# =============================================================================
# Link build statements for EXECUTABLE target osg_compatibility_test


#############################################
# Link the executable osg_compatibility_test.html

build osg_compatibility_test.html: CXX_EXECUTABLE_LINKER__osg_compatibility_test_Release CMakeFiles/osg_compatibility_test.dir/OSGInterfaceTestFramework.cpp.o CMakeFiles/osg_compatibility_test.dir/CoreRenderingTests.cpp.o CMakeFiles/osg_compatibility_test.dir/main.cpp.o | F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a F$:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a
  FLAGS = -O3 -DNDEBUG
  LINK_FLAGS = -s USE_WEBGL2=1 -s FULL_ES3=1 -s WASM=1 -s ALLOW_MEMORY_GROWTH=1 -s SAFE_HEAP=0 -s ASSERTIONS=0 -s DISABLE_EXCEPTION_CATCHING=1 -s NO_EXIT_RUNTIME=1 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/shell.html
  LINK_LIBRARIES = F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a  F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a  -lGL  -lGLU  -lX11
  OBJECT_DIR = CMakeFiles\osg_compatibility_test.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = osg_compatibility_test.html
  TARGET_PDB = osg_compatibility_test.html.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250714\tests\osg_interface_compatibility\build_wasm && "C:\Program Files\CMake\bin\cmake-gui.exe" -SF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250714\tests\osg_interface_compatibility -BF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250714\tests\osg_interface_compatibility\build_wasm"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250714\tests\osg_interface_compatibility\build_wasm && "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250714\tests\osg_interface_compatibility -BF:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250714\tests\osg_interface_compatibility\build_wasm"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build osg_compatibility_test: phony osg_compatibility_test.html

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_wasm

build all: phony osg_compatibility_test.html

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-C.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-CXX.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/GNU.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/FindOpenSceneGraph.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/FindOpenThreads.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/FindPackageHandleStandardArgs.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/FindPackageMessage.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Findosg.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/FindosgDB.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/FindosgGA.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/FindosgUtil.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/FindosgViewer.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Findosg_functions.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/SelectLibraryConfigurations.cmake C$:/dev/emsdk/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake CMakeCache.txt CMakeFiles/3.26.4/CMakeCCompiler.cmake CMakeFiles/3.26.4/CMakeCXXCompiler.cmake CMakeFiles/3.26.4/CMakeSystem.cmake F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-C.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang-CXX.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/Clang.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Compiler/GNU.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/FindOpenSceneGraph.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/FindOpenThreads.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/FindPackageHandleStandardArgs.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/FindPackageMessage.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Findosg.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/FindosgDB.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/FindosgGA.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/FindosgUtil.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/FindosgViewer.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/Findosg_functions.cmake C$:/Program$ Files/CMake/share/cmake-3.26/Modules/SelectLibraryConfigurations.cmake C$:/dev/emsdk/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake CMakeCache.txt CMakeFiles/3.26.4/CMakeCCompiler.cmake CMakeFiles/3.26.4/CMakeCXXCompiler.cmake CMakeFiles/3.26.4/CMakeSystem.cmake F$:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
