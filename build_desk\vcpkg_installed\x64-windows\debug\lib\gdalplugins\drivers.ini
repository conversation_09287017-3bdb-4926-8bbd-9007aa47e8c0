################################################################################
# Order into which drivers should be registered.
# Normally this is entirely defined in frmts/gdalallregister.cpp and
# ogr/ogrsf_frmts/generic/ogrregisterall.cpp for drivers embedded in libgdal
# But for drivers built as plugin, without that file, they would be registered
# in the order the operating system returns filenames in the $prefix/lib/gdalplugins
# directory
#
# KEEP IN SYNC with frmts/gdalallregister.cpp and
# ogr/ogrsf_frmts/generic/ogrregisterall.cpp
################################################################################

[order]
VRT
Derived
GTI
SNAP_TIFF
GTiff
COG
LIBERTIFF
NITF
RPFTOC
ECRGTOC
HFA
SAR_CEOS
CEOS
JAXAPALSAR
GFF
ESRIC
AIG
AAIGrid
GRASSASCIIGrid
ISG
DTED
PNG
DDS
GTA
JPEG
MEM
JDEM
RASDAMAN
GIF
BIGGIF
ESAT
FITS
BSB
BMP
DIMAP
AirSAR
RS2
SAFE
PCIDSK
PCRaster
ILWIS
SRTMHGT
Leveller
Terragen
netCDF
HDF4
HDF4Image
ISIS3
ISIS2
PDS
PDS4
VICAR
TIL
ERS
JP2KAK
JPIPKAK
ECW
JP2ECW
JP2OpenJPEG
L1B
GRIB
MrSID
JP2MrSID
JPEG2000
RMF
WCS
WMS
MSGN
MSG
RST
GS7BG
COSAR
TSX
COASP
R
MAP
KMLSUPEROVERLAY
WEBP
PDF
Rasterlite
MBTiles
PLMOSAIC
CALS
WMTS
SENTINEL2
MRF
TileDB
PNM
DOQ1
DOQ2
PAux
MFF
MFF2
GSC
FAST
LAN
CPG
NDF
EIR
LCP
GTX
LOSLAS
NTv2
ACE2
SNODAS
KRO
ROI_PAC
RRASTER
BYN
NOAA_B
RIK
USGSDEM
GXF
KEA
BAG
S102
S104
S111
HDF5
HDF5Image
NWT_GRD
NWT_GRC
ADRG
SRP
GeoRaster
PostGISRaster
SAGA
XYZ
HF2
CTG
ZMap
NGSGEOID
IRIS
PRF
RDA
EEDAI
EEDA
DAAS
NULL
SIGDEM
EXR
AVIF
HEIF
TGA
OGCAPI
STACTA
STACIT
JPEGXL
BASISU
KTX2
GDALG
NSIDCbin

# GNM drivers
GNMFile
GNMDatabase
# End of GNM drivers

# Beginning OGR drivers
ESRI Shapefile
MapInfo File
LVBAG
S57
DGN
OGR_VRT
CSV
NAS
GML
GPX
LIBKML
KML
GeoJSON
GeoJSONSeq
ESRIJSON
TopoJSON
Interlis 1
Interlis 2
OGR_GMT
GPKG
SQLite
ODBC
WAsP
PGeo
MSSQLSpatial
PostgreSQL
MySQL
OCI
# Register OpenFileGDB before FGDB as it is more capable for read-only
OpenFileGDB
FileGDB
DWG
DGNV8
DXF
CAD
FlatGeobuf
FME
IDB
Geoconcept
GeoRSS
GPSTrackMaker
VFK
PGDump
# Register OSM before GPSBabel, that could recognize .osm file too
OSM
GPSBabel
OGR_PDS
WFS
OAPIF
SOSI
EDIGEO
IDRISI
XLS
ODS
XLSX
ElasticSearch
Walk
Carto
AmigoCloud
SXF
Selafin
JML
PLSCENES
CSW
MongoDBv3
VDV
GMLAS
MVT
NGW
MapML
HANA
Parquet
Arrow
GTFS
PMTiles
JSONFG
MiraMonVector
XODR
ADBC

# Put AVCBIN at end since they need poOpenInfo->GetSiblingFiles()
AVCBin
AVCE00

# Last but not the least
AIVector

# End of OGR drivers

# Put here drivers that absolutely need to look for side car
# files in their Identify()/Open() procedure.
GenBin
ENVI
EHdr
ISCE
Zarr
RCM

# Register GDAL HTTP last, to let a chance to other drivers
# accepting URL to handle them before
HTTP

# Driver registered by Python bindings
NUMPY
