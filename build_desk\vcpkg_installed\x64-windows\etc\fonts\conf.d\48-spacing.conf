<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "urn:fontconfig:fonts.dtd">
<fontconfig>
  <description>Add mono to the family when spacing is 100</description>
<!--
  If the request specifies spacing 100, add monospace to family
 -->
	<match target="pattern">
		<test qual="any" name="spacing" compare="eq">
			<int>100</int>
		</test>
		<edit name="family" mode="prepend">
			<string>monospace</string>
		</edit>
	</match>
</fontconfig>
