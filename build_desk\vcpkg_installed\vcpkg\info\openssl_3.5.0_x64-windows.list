x64-windows/
x64-windows/bin/
x64-windows/bin/legacy.dll
x64-windows/bin/legacy.pdb
x64-windows/bin/libcrypto-3-x64.dll
x64-windows/bin/libcrypto-3-x64.pdb
x64-windows/bin/libssl-3-x64.dll
x64-windows/bin/libssl-3-x64.pdb
x64-windows/debug/
x64-windows/debug/bin/
x64-windows/debug/bin/legacy.dll
x64-windows/debug/bin/legacy.pdb
x64-windows/debug/bin/libcrypto-3-x64.dll
x64-windows/debug/bin/libcrypto-3-x64.pdb
x64-windows/debug/bin/libssl-3-x64.dll
x64-windows/debug/bin/libssl-3-x64.pdb
x64-windows/debug/lib/
x64-windows/debug/lib/libcrypto.lib
x64-windows/debug/lib/libssl.lib
x64-windows/debug/lib/pkgconfig/
x64-windows/debug/lib/pkgconfig/libcrypto.pc
x64-windows/debug/lib/pkgconfig/libssl.pc
x64-windows/debug/lib/pkgconfig/openssl.pc
x64-windows/include/
x64-windows/include/openssl/
x64-windows/include/openssl/__DECC_INCLUDE_EPILOGUE.H
x64-windows/include/openssl/__DECC_INCLUDE_PROLOGUE.H
x64-windows/include/openssl/aes.h
x64-windows/include/openssl/applink.c
x64-windows/include/openssl/asn1.h
x64-windows/include/openssl/asn1err.h
x64-windows/include/openssl/asn1t.h
x64-windows/include/openssl/async.h
x64-windows/include/openssl/asyncerr.h
x64-windows/include/openssl/bio.h
x64-windows/include/openssl/bioerr.h
x64-windows/include/openssl/blowfish.h
x64-windows/include/openssl/bn.h
x64-windows/include/openssl/bnerr.h
x64-windows/include/openssl/buffer.h
x64-windows/include/openssl/buffererr.h
x64-windows/include/openssl/byteorder.h
x64-windows/include/openssl/camellia.h
x64-windows/include/openssl/cast.h
x64-windows/include/openssl/cmac.h
x64-windows/include/openssl/cmp.h
x64-windows/include/openssl/cmp_util.h
x64-windows/include/openssl/cmperr.h
x64-windows/include/openssl/cms.h
x64-windows/include/openssl/cmserr.h
x64-windows/include/openssl/comp.h
x64-windows/include/openssl/comperr.h
x64-windows/include/openssl/conf.h
x64-windows/include/openssl/conf_api.h
x64-windows/include/openssl/conferr.h
x64-windows/include/openssl/configuration.h
x64-windows/include/openssl/conftypes.h
x64-windows/include/openssl/core.h
x64-windows/include/openssl/core_dispatch.h
x64-windows/include/openssl/core_names.h
x64-windows/include/openssl/core_object.h
x64-windows/include/openssl/crmf.h
x64-windows/include/openssl/crmferr.h
x64-windows/include/openssl/crypto.h
x64-windows/include/openssl/cryptoerr.h
x64-windows/include/openssl/cryptoerr_legacy.h
x64-windows/include/openssl/ct.h
x64-windows/include/openssl/cterr.h
x64-windows/include/openssl/decoder.h
x64-windows/include/openssl/decodererr.h
x64-windows/include/openssl/des.h
x64-windows/include/openssl/dh.h
x64-windows/include/openssl/dherr.h
x64-windows/include/openssl/dsa.h
x64-windows/include/openssl/dsaerr.h
x64-windows/include/openssl/dtls1.h
x64-windows/include/openssl/e_os2.h
x64-windows/include/openssl/e_ostime.h
x64-windows/include/openssl/ebcdic.h
x64-windows/include/openssl/ec.h
x64-windows/include/openssl/ecdh.h
x64-windows/include/openssl/ecdsa.h
x64-windows/include/openssl/ecerr.h
x64-windows/include/openssl/encoder.h
x64-windows/include/openssl/encodererr.h
x64-windows/include/openssl/engine.h
x64-windows/include/openssl/engineerr.h
x64-windows/include/openssl/err.h
x64-windows/include/openssl/ess.h
x64-windows/include/openssl/esserr.h
x64-windows/include/openssl/evp.h
x64-windows/include/openssl/evperr.h
x64-windows/include/openssl/fips_names.h
x64-windows/include/openssl/fipskey.h
x64-windows/include/openssl/hmac.h
x64-windows/include/openssl/hpke.h
x64-windows/include/openssl/http.h
x64-windows/include/openssl/httperr.h
x64-windows/include/openssl/idea.h
x64-windows/include/openssl/indicator.h
x64-windows/include/openssl/kdf.h
x64-windows/include/openssl/kdferr.h
x64-windows/include/openssl/lhash.h
x64-windows/include/openssl/macros.h
x64-windows/include/openssl/md2.h
x64-windows/include/openssl/md4.h
x64-windows/include/openssl/md5.h
x64-windows/include/openssl/mdc2.h
x64-windows/include/openssl/ml_kem.h
x64-windows/include/openssl/modes.h
x64-windows/include/openssl/obj_mac.h
x64-windows/include/openssl/objects.h
x64-windows/include/openssl/objectserr.h
x64-windows/include/openssl/ocsp.h
x64-windows/include/openssl/ocsperr.h
x64-windows/include/openssl/opensslconf.h
x64-windows/include/openssl/opensslv.h
x64-windows/include/openssl/ossl_typ.h
x64-windows/include/openssl/param_build.h
x64-windows/include/openssl/params.h
x64-windows/include/openssl/pem.h
x64-windows/include/openssl/pem2.h
x64-windows/include/openssl/pemerr.h
x64-windows/include/openssl/pkcs12.h
x64-windows/include/openssl/pkcs12err.h
x64-windows/include/openssl/pkcs7.h
x64-windows/include/openssl/pkcs7err.h
x64-windows/include/openssl/prov_ssl.h
x64-windows/include/openssl/proverr.h
x64-windows/include/openssl/provider.h
x64-windows/include/openssl/quic.h
x64-windows/include/openssl/rand.h
x64-windows/include/openssl/randerr.h
x64-windows/include/openssl/rc2.h
x64-windows/include/openssl/rc4.h
x64-windows/include/openssl/rc5.h
x64-windows/include/openssl/ripemd.h
x64-windows/include/openssl/rsa.h
x64-windows/include/openssl/rsaerr.h
x64-windows/include/openssl/safestack.h
x64-windows/include/openssl/seed.h
x64-windows/include/openssl/self_test.h
x64-windows/include/openssl/sha.h
x64-windows/include/openssl/srp.h
x64-windows/include/openssl/srtp.h
x64-windows/include/openssl/ssl.h
x64-windows/include/openssl/ssl2.h
x64-windows/include/openssl/ssl3.h
x64-windows/include/openssl/sslerr.h
x64-windows/include/openssl/sslerr_legacy.h
x64-windows/include/openssl/stack.h
x64-windows/include/openssl/store.h
x64-windows/include/openssl/storeerr.h
x64-windows/include/openssl/symhacks.h
x64-windows/include/openssl/thread.h
x64-windows/include/openssl/tls1.h
x64-windows/include/openssl/trace.h
x64-windows/include/openssl/ts.h
x64-windows/include/openssl/tserr.h
x64-windows/include/openssl/txt_db.h
x64-windows/include/openssl/types.h
x64-windows/include/openssl/ui.h
x64-windows/include/openssl/uierr.h
x64-windows/include/openssl/whrlpool.h
x64-windows/include/openssl/x509.h
x64-windows/include/openssl/x509_acert.h
x64-windows/include/openssl/x509_vfy.h
x64-windows/include/openssl/x509err.h
x64-windows/include/openssl/x509v3.h
x64-windows/include/openssl/x509v3err.h
x64-windows/lib/
x64-windows/lib/libcrypto.lib
x64-windows/lib/libssl.lib
x64-windows/lib/pkgconfig/
x64-windows/lib/pkgconfig/libcrypto.pc
x64-windows/lib/pkgconfig/libssl.pc
x64-windows/lib/pkgconfig/openssl.pc
x64-windows/share/
x64-windows/share/openssl/
x64-windows/share/openssl/OpenSSLConfig.cmake
x64-windows/share/openssl/OpenSSLConfigVersion.cmake
x64-windows/share/openssl/copyright
x64-windows/share/openssl/usage
x64-windows/share/openssl/vcpkg-cmake-wrapper.cmake
x64-windows/share/openssl/vcpkg.spdx.json
x64-windows/share/openssl/vcpkg_abi_info.txt
