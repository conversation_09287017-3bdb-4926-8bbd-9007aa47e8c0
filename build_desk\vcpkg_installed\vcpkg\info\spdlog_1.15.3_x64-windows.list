x64-windows/
x64-windows/bin/
x64-windows/bin/spdlog.dll
x64-windows/bin/spdlog.pdb
x64-windows/debug/
x64-windows/debug/bin/
x64-windows/debug/bin/spdlogd.dll
x64-windows/debug/bin/spdlogd.pdb
x64-windows/debug/lib/
x64-windows/debug/lib/pkgconfig/
x64-windows/debug/lib/pkgconfig/spdlog.pc
x64-windows/debug/lib/spdlogd.lib
x64-windows/include/
x64-windows/include/spdlog/
x64-windows/include/spdlog/async.h
x64-windows/include/spdlog/async_logger-inl.h
x64-windows/include/spdlog/async_logger.h
x64-windows/include/spdlog/cfg/
x64-windows/include/spdlog/cfg/argv.h
x64-windows/include/spdlog/cfg/env.h
x64-windows/include/spdlog/cfg/helpers-inl.h
x64-windows/include/spdlog/cfg/helpers.h
x64-windows/include/spdlog/common-inl.h
x64-windows/include/spdlog/common.h
x64-windows/include/spdlog/details/
x64-windows/include/spdlog/details/backtracer-inl.h
x64-windows/include/spdlog/details/backtracer.h
x64-windows/include/spdlog/details/circular_q.h
x64-windows/include/spdlog/details/console_globals.h
x64-windows/include/spdlog/details/file_helper-inl.h
x64-windows/include/spdlog/details/file_helper.h
x64-windows/include/spdlog/details/fmt_helper.h
x64-windows/include/spdlog/details/log_msg-inl.h
x64-windows/include/spdlog/details/log_msg.h
x64-windows/include/spdlog/details/log_msg_buffer-inl.h
x64-windows/include/spdlog/details/log_msg_buffer.h
x64-windows/include/spdlog/details/mpmc_blocking_q.h
x64-windows/include/spdlog/details/null_mutex.h
x64-windows/include/spdlog/details/os-inl.h
x64-windows/include/spdlog/details/os.h
x64-windows/include/spdlog/details/periodic_worker-inl.h
x64-windows/include/spdlog/details/periodic_worker.h
x64-windows/include/spdlog/details/registry-inl.h
x64-windows/include/spdlog/details/registry.h
x64-windows/include/spdlog/details/synchronous_factory.h
x64-windows/include/spdlog/details/tcp_client-windows.h
x64-windows/include/spdlog/details/tcp_client.h
x64-windows/include/spdlog/details/thread_pool-inl.h
x64-windows/include/spdlog/details/thread_pool.h
x64-windows/include/spdlog/details/udp_client-windows.h
x64-windows/include/spdlog/details/udp_client.h
x64-windows/include/spdlog/details/windows_include.h
x64-windows/include/spdlog/fmt/
x64-windows/include/spdlog/fmt/bin_to_hex.h
x64-windows/include/spdlog/fmt/chrono.h
x64-windows/include/spdlog/fmt/compile.h
x64-windows/include/spdlog/fmt/fmt.h
x64-windows/include/spdlog/fmt/ostr.h
x64-windows/include/spdlog/fmt/ranges.h
x64-windows/include/spdlog/fmt/std.h
x64-windows/include/spdlog/fmt/xchar.h
x64-windows/include/spdlog/formatter.h
x64-windows/include/spdlog/fwd.h
x64-windows/include/spdlog/logger-inl.h
x64-windows/include/spdlog/logger.h
x64-windows/include/spdlog/mdc.h
x64-windows/include/spdlog/pattern_formatter-inl.h
x64-windows/include/spdlog/pattern_formatter.h
x64-windows/include/spdlog/sinks/
x64-windows/include/spdlog/sinks/android_sink.h
x64-windows/include/spdlog/sinks/ansicolor_sink-inl.h
x64-windows/include/spdlog/sinks/ansicolor_sink.h
x64-windows/include/spdlog/sinks/base_sink-inl.h
x64-windows/include/spdlog/sinks/base_sink.h
x64-windows/include/spdlog/sinks/basic_file_sink-inl.h
x64-windows/include/spdlog/sinks/basic_file_sink.h
x64-windows/include/spdlog/sinks/callback_sink.h
x64-windows/include/spdlog/sinks/daily_file_sink.h
x64-windows/include/spdlog/sinks/dist_sink.h
x64-windows/include/spdlog/sinks/dup_filter_sink.h
x64-windows/include/spdlog/sinks/hourly_file_sink.h
x64-windows/include/spdlog/sinks/kafka_sink.h
x64-windows/include/spdlog/sinks/mongo_sink.h
x64-windows/include/spdlog/sinks/msvc_sink.h
x64-windows/include/spdlog/sinks/null_sink.h
x64-windows/include/spdlog/sinks/ostream_sink.h
x64-windows/include/spdlog/sinks/qt_sinks.h
x64-windows/include/spdlog/sinks/ringbuffer_sink.h
x64-windows/include/spdlog/sinks/rotating_file_sink-inl.h
x64-windows/include/spdlog/sinks/rotating_file_sink.h
x64-windows/include/spdlog/sinks/sink-inl.h
x64-windows/include/spdlog/sinks/sink.h
x64-windows/include/spdlog/sinks/stdout_color_sinks-inl.h
x64-windows/include/spdlog/sinks/stdout_color_sinks.h
x64-windows/include/spdlog/sinks/stdout_sinks-inl.h
x64-windows/include/spdlog/sinks/stdout_sinks.h
x64-windows/include/spdlog/sinks/syslog_sink.h
x64-windows/include/spdlog/sinks/systemd_sink.h
x64-windows/include/spdlog/sinks/tcp_sink.h
x64-windows/include/spdlog/sinks/udp_sink.h
x64-windows/include/spdlog/sinks/win_eventlog_sink.h
x64-windows/include/spdlog/sinks/wincolor_sink-inl.h
x64-windows/include/spdlog/sinks/wincolor_sink.h
x64-windows/include/spdlog/spdlog-inl.h
x64-windows/include/spdlog/spdlog.h
x64-windows/include/spdlog/stopwatch.h
x64-windows/include/spdlog/tweakme.h
x64-windows/include/spdlog/version.h
x64-windows/lib/
x64-windows/lib/pkgconfig/
x64-windows/lib/pkgconfig/spdlog.pc
x64-windows/lib/spdlog.lib
x64-windows/share/
x64-windows/share/spdlog/
x64-windows/share/spdlog/copyright
x64-windows/share/spdlog/spdlogConfig.cmake
x64-windows/share/spdlog/spdlogConfigTargets-debug.cmake
x64-windows/share/spdlog/spdlogConfigTargets-release.cmake
x64-windows/share/spdlog/spdlogConfigTargets.cmake
x64-windows/share/spdlog/spdlogConfigVersion.cmake
x64-windows/share/spdlog/usage
x64-windows/share/spdlog/vcpkg.spdx.json
x64-windows/share/spdlog/vcpkg_abi_info.txt
