cmake 3.30.1
cmakelists.patch 305aebc75d602ee4624eba33f04dd3d12cc082b2aa75f0123195b25acf4e8812
features core
portfile.cmake 5e4a38154ac4152264597fbaf94df2c61360cf289fcd3784e2d9873d08bb3f65
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.2
proj 05d2ab5643e4323b6acb1f92cf0d6c23cb8b8b345bb9ee6685561a242465af05
tiff 4064975610ad67a83507450cbf0f021965873276def31c617cc59ad285cff373
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
usage efa371d009799cc46a775e388ce59a310448f7b4c09b885f9e906011420f475d
vcpkg-cmake a0dc1d028307cd46ec7e45b561314f30e28ec952c29f9749d9aac9bdf5fed759
vcpkg-cmake-config 472514a630e790ecc84c4109c9d53da233d4ffd78017b825f9117c89e190f16e
vcpkg.json e20ce100d45ed2c6544c668e7b073e92a607f3c061ef4e272c102b7d0da13174
vcpkg_check_features 943b217e0968d64cf2cb9c272608e6a0b497377e792034f819809a79e1502c2b
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_copy_tools 3d45ff761bddbabe8923b52330168dc3abd295fa469d3f2e47cb14dce85332d5
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
