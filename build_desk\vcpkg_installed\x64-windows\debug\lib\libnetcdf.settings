# NetCDF C Configuration Summary
==============================

# General
-------
NetCDF Version:		4.8.1
Dispatch Version:       3
Configured On:		
Host System:		--
Build Directory: 	C:/dev/vcpkg/buildtrees/netcdf-c/x64-windows-dbg
Install Prefix:         C:/dev/vcpkg/packages/netcdf-c_x64-windows/debug

# Compiling Options
-----------------
C Compiler:		C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe
CFLAGS:			 /nologo /DWIN32 /D_WINDOWS /utf-8 /MP  /MDd /Z7 /Ob0 /Od /RTC1 
CPPFLAGS:		 
LDFLAGS:		/machine:x64 /LARGEADDRESSAWARE /STACK:40000000 /nologo    /debug /INCREMENTAL
AM_CFLAGS:		
AM_CPPFLAGS:		
AM_LDFLAGS:		
Shared Library:		yes
Static Library:		no
Extra libraries:	-lhdf5 -lhdf5_hl -lCURL::libcurl

# Features
--------
NetCDF-2 API:		yes
HDF4 Support:		no
HDF5 Support:		yes
NetCDF-4 API:		yes
NC-4 Parallel Support:	no
PnetCDF Support:	no
DAP2 Support:		yes
DAP4 Support:		yes
Byte-Range Support:	no
Diskless Support:	yes
MMap Support:		no
JNA Support:		no
CDF5 Support:		yes
ERANGE Fill Support:	yes
Relaxed Boundary Check:	yes
SZIP Support:           yes
SZIP Write Support:     yes
Parallel Filters:       yes
NCZarr Support:		yes
Multi-Filter Support:	yes
