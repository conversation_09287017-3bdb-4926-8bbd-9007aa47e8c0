@echo off
chcp 65001 >nul
echo ============================================================
echo 🌍 OSGEarth WebAssembly 快速启动
echo ============================================================
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ 未找到 Python 解释器
    echo 请确保已安装 Python 并添加到系统PATH
    echo.
    echo 推荐安装: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python 环境检测成功
python --version

echo.
echo 🚀 正在启动 OSGEarth WebAssembly 服务器...
echo    默认端口: 8080
echo    访问地址: http://localhost:8080/index.html
echo.

REM 启动Python服务器
python run_server.py
REM python start_server_with_headers.py

echo.
echo 👋 感谢使用 OSGEarth WebAssembly 版本！
pause 