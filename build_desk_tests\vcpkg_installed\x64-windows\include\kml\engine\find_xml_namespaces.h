// Copyright 2009, Google Inc. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//
//  1. Redistributions of source code must retain the above copyright notice,
//     this list of conditions and the following disclaimer.
//  2. Redistributions in binary form must reproduce the above copyright notice,
//     this list of conditions and the following disclaimer in the documentation
//     and/or other materials provided with the distribution.
//  3. Neither the name of Google Inc. nor the names of its contributors may be
//     used to endorse or promote products derived from this software without
//     specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
// WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
// MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
// EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON> DAMAGES (INCLUDING, BUT NOT LIMITED TO,
// PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
// OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
// WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
// OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF
// ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// This file contains the declaration of the GetElementsByType() and the
// internal ElementFinder class.

#ifndef KML_ENGINE_FIND_XML_NAMESPACES_H__
#define KML_ENGINE_FIND_XML_NAMESPACES_H__

#include "kml/dom.h"

namespace kmlbase {
class Attributes;
}

namespace kmlengine {

// This creates one attribute name-value pair for each XML namespace found in
// the hierarchy rooted at the given element.  The namespaces must be in the
// kmlbase::XmlnsId list and the prefix and xml_namespace is as is found by
// kmlbase::FindXmlNamespaceAndPrefix.  Unknown namespaces are ignored.
void FindXmlNamespaces(const kmldom::ElementPtr& element,
                       kmlbase::Attributes* xmlns_attributes);

// This calls FindXmlNamespaces() and inserts the resulting xmlns
// prefix/namespace pairs.  The KML namespace is special cased as the default
// namespace (xmlns="...") if any KML elements are present.  All other
// namespaces are prefixed with the libkml-standard prefixes (see
// kmlbase::FindXmlNamespaceAndPrefix().
void FindAndInsertXmlNamespaces(kmldom::ElementPtr element);

}  // end namespace kmlengine

#endif  // KML_ENGINE_FIND_XML_NAMESPACES_H__
