{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/json-c-x64-windows-0.18-20240915-4dcdcc2b-76e8-474f-8d7b-e415ef7fd1cd", "name": "json-c:x64-windows@0.18-20240915 a9ca0f35a913f6b179d89e05f02d53272b7eccebbe8edbc72d27aaae14b65d69", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-14T04:47:02Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "json-c", "SPDXID": "SPDXRef-port", "versionInfo": "0.18-20240915", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/json-c", "homepage": "https://github.com/json-c/json-c", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "A JSON implementation in C", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "json-c:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "a9ca0f35a913f6b179d89e05f02d53272b7eccebbe8edbc72d27aaae14b65d69", "downloadLocation": "NONE", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "json-c/json-c", "downloadLocation": "git+https://github.com/json-c/json-c@json-c-0.18-20240915", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "219d8c0da9a4016b74af238cc15dbec1f369a07de160bcc548d80279028e1b5d8d928deb13fec09c96a085fc0ecf10090e309cbe72d0081aca864433c4ae01db"}]}], "files": [{"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "6cf58e85f5ddaa1485afc3c0e8fbe485779252fc3e6bdedad5152eaec87eb257"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "1be31b9fb135a8a5665412a79996f29da791da0cf364722da49852f5b28da535"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}