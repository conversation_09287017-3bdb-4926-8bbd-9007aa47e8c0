add-target-include-directories.patch a515b9ced6710aff8a059aba9bd5352ea93227a622a99bb4083fc984b138ea70
boost-smart-ptr 25396aca0569446e752ae3ff2c4d95128a998a61ecab3f85f367cf069f847f48
cmake 3.30.1
expat c1bd22cf086a22a0064d19a2d6d29430c98ecf59ecc5ceec8266b03c04144c01
features core
fix-mingw.patch e4c730942f6f285a6a16d401b66558847d98812d90964afbeef13b782b02103c
fix-minizip.patch 759f1082b89f5dcb331edcbdca7513bede78ee2c0d2a5374d476e90f11db6017
minizip aeb3ad78c96175aba939e19afce67ce1199c17637270994cf5f90cbc67544d55
patch_empty_literal_on_vc.patch 6d39d60da6c0fd3fd24e319fce03d9db852d3e7f4f17c7a2942411894882e389
portfile.cmake 68f8e34f266f2c428023b2aad0a09ccc9663b16e47200065b6109b75ad4b40f2
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.2
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
uriparser a7d507e5dd11287993b54d046f81354761cc328571980fe4ba7372dd14f1f56b
vcpkg-cmake a0dc1d028307cd46ec7e45b561314f30e28ec952c29f9749d9aac9bdf5fed759
vcpkg-cmake-config 472514a630e790ecc84c4109c9d53da233d4ffd78017b825f9117c89e190f16e
vcpkg.json 4ee30ab84b4f1fced8fddce88682847f355889c7932871b7965d63619e4991e2
vcpkg_check_linkage f8d5dc4ee94493155b76fb825a74e9c4f6037569b990c3f8c02074338223fbbd
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
zlib e746177ce0372a687f2c13895e5068f2ed171777713c6e8fe0fe222816077de3
