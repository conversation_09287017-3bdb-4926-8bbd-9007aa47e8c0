{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/lerc-x64-windows-4.0.4-a27bb5db-f6d8-4e96-817f-33e42aa39e4c", "name": "lerc:x64-windows@4.0.4 b155fd26309a7ea3c707bf71e500788310fe79d746ae491301ae0d2998ed1c0b", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-14T04:46:21Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-3"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-4"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-4", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-4", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "lerc", "SPDXID": "SPDXRef-port", "versionInfo": "4.0.4", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/lerc", "homepage": "https://github.com/Esri/lerc", "licenseConcluded": "Apache-2.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "An open-source image or raster format which supports rapid encoding and decoding for any pixel type", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "lerc:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "b155fd26309a7ea3c707bf71e500788310fe79d746ae491301ae0d2998ed1c0b", "downloadLocation": "NONE", "licenseConcluded": "Apache-2.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "<PERSON>s<PERSON>/lerc", "downloadLocation": "git+https://github.com/Esri/lerc@js_v4.0.4", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "061558d3b29e2d0968d1169ac422795faa6e70dd3425945194c1c87f4522422e186878b0235a5fc42f037c47c54964bf070b7644f8d652f33dc19f692a6ba0af"}]}], "files": [{"fileName": "./create_package.patch", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "7829909725404e8328af4c5959c73b4fe1ec50481b11769d343d79925ce1b8e6"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./cxx-linkage-pkgconfig.patch", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "cb3b6d8750603ad3e3dfcd3e8072ff36066e213065c007b6fea96b28360e674d"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./fix-climits-include.patch", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "ee87bca77c4057a47ed1692f7e6cf66babc2b391629d672cc682d03c2672a915"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-3", "checksums": [{"algorithm": "SHA256", "checksumValue": "1450966caea88c9b70a837be464bb9cad0ccba6dca8c2be38031e3404bcfd184"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-4", "checksums": [{"algorithm": "SHA256", "checksumValue": "a4bc75532c10e7715b5f43c74b38407dfcd8f1e8e8b7d7be908ce9678513d57f"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}