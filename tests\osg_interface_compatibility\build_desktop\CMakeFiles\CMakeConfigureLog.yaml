
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake:204 (message)"
      - "CMakeLists.txt:7 (project)"
    message: |
      The system is: Windows - 10.0.26058 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:7 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      适用于 .NET Framework MSBuild 版本 17.14.14+a129329f1
      生成启动时间为 2025/7/14 13:08:02。
      
      节点 1 上的项目“F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\3.26.4\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
        正在创建目录“Debug\\CompilerIdC.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        正在对“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”执行 Touch 任务。
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "C:\\dev\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      VcpkgCheckManifestRoot:
        The vcpkg manifest was disabled, but we found a manifest file in F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\. You may want to enable vcpkg manifests in your properties page or pass /p:VcpkgEnableManifest=true to the msbuild invocation.
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\dev\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "C:\\dev\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\3.26.4\\CompilerIdC\\CompilerIdC.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\dev\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\3.26.4\\CompilerIdC\\CompilerIdC.exe" "C:\\dev\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate”执行 Touch 任务。
      已完成生成项目“F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\3.26.4\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:03.26
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_desktop/CMakeFiles/3.26.4/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:7 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      适用于 .NET Framework MSBuild 版本 17.14.14+a129329f1
      生成启动时间为 2025/7/14 13:08:05。
      
      节点 1 上的项目“F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\3.26.4\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
        正在创建目录“Debug\\CompilerIdCXX.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        正在对“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”执行 Touch 任务。
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "C:\\dev\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      VcpkgCheckManifestRoot:
        The vcpkg manifest was disabled, but we found a manifest file in F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\. You may want to enable vcpkg manifests in your properties page or pass /p:VcpkgEnableManifest=true to the msbuild invocation.
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\dev\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "C:\\dev\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\3.26.4\\CompilerIdCXX\\CompilerIdCXX.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\dev\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\3.26.4\\CompilerIdCXX\\CompilerIdCXX.exe" "C:\\dev\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate”执行 Touch 任务。
      已完成生成项目“F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\3.26.4\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:04.16
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_desktop/CMakeFiles/3.26.4/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:7 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_desktop/CMakeFiles/CMakeScratch/TryCompile-c3flhx"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_desktop/CMakeFiles/CMakeScratch/TryCompile-c3flhx"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/dev/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/dev/vcpkg"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_desktop/CMakeFiles/CMakeScratch/TryCompile-c3flhx
        
        Run Build Command(s):C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_fa490.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n && 适用于 .NET Framework MSBuild 版本 17.14.14+a129329f1
        生成启动时间为 2025/7/14 13:08:10。
        
        节点 1 上的项目“F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\CMakeScratch\\TryCompile-c3flhx\\cmTC_fa490.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_fa490.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\CMakeScratch\\TryCompile-c3flhx\\Debug\\”。
          正在创建目录“cmTC_fa490.dir\\Debug\\cmTC_fa490.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_fa490.dir\\Debug\\cmTC_fa490.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_fa490.dir\\Debug\\cmTC_fa490.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_fa490.dir\\Debug\\\\" /Fd"cmTC_fa490.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.26\\Modules\\CMakeCCompilerABI.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35211 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_fa490.dir\\Debug\\\\" /Fd"cmTC_fa490.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.26\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\CMakeScratch\\TryCompile-c3flhx\\Debug\\cmTC_fa490.exe" /INCREMENTAL /ILK:"cmTC_fa490.dir\\Debug\\cmTC_fa490.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_desktop/CMakeFiles/CMakeScratch/TryCompile-c3flhx/Debug/cmTC_fa490.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_desktop/CMakeFiles/CMakeScratch/TryCompile-c3flhx/Debug/cmTC_fa490.lib" /MACHINE:X64  /machine:x64 cmTC_fa490.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_fa490.vcxproj -> F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\CMakeScratch\\TryCompile-c3flhx\\Debug\\cmTC_fa490.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_fa490.dir\\Debug\\cmTC_fa490.tlog\\unsuccessfulbuild”。
          正在对“cmTC_fa490.dir\\Debug\\cmTC_fa490.tlog\\cmTC_fa490.lastbuildstate”执行 Touch 任务。
        已完成生成项目“F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\CMakeScratch\\TryCompile-c3flhx\\cmTC_fa490.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:00.97
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:7 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_desktop/CMakeFiles/CMakeScratch/TryCompile-p3c2p6"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_desktop/CMakeFiles/CMakeScratch/TryCompile-p3c2p6"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/dev/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/dev/vcpkg"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_desktop/CMakeFiles/CMakeScratch/TryCompile-p3c2p6
        
        Run Build Command(s):C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_6f839.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n && 适用于 .NET Framework MSBuild 版本 17.14.14+a129329f1
        生成启动时间为 2025/7/14 13:08:11。
        
        节点 1 上的项目“F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\CMakeScratch\\TryCompile-p3c2p6\\cmTC_6f839.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_6f839.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\CMakeScratch\\TryCompile-p3c2p6\\Debug\\”。
          正在创建目录“cmTC_6f839.dir\\Debug\\cmTC_6f839.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_6f839.dir\\Debug\\cmTC_6f839.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_6f839.dir\\Debug\\cmTC_6f839.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_6f839.dir\\Debug\\\\" /Fd"cmTC_6f839.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.26\\Modules\\CMakeCXXCompilerABI.cpp"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35211 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_6f839.dir\\Debug\\\\" /Fd"cmTC_6f839.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.26\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\CMakeScratch\\TryCompile-p3c2p6\\Debug\\cmTC_6f839.exe" /INCREMENTAL /ILK:"cmTC_6f839.dir\\Debug\\cmTC_6f839.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_desktop/CMakeFiles/CMakeScratch/TryCompile-p3c2p6/Debug/cmTC_6f839.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_desktop/CMakeFiles/CMakeScratch/TryCompile-p3c2p6/Debug/cmTC_6f839.lib" /MACHINE:X64  /machine:x64 cmTC_6f839.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_6f839.vcxproj -> F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\CMakeScratch\\TryCompile-p3c2p6\\Debug\\cmTC_6f839.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_6f839.dir\\Debug\\cmTC_6f839.tlog\\unsuccessfulbuild”。
          正在对“cmTC_6f839.dir\\Debug\\cmTC_6f839.tlog\\cmTC_6f839.lastbuildstate”执行 Touch 任务。
        已完成生成项目“F:\\cmo-dev\\my_osgearth_web\\osgearth_origin\\myosgearth_degdalproj20250714\\tests\\osg_interface_compatibility\\build_desktop\\CMakeFiles\\CMakeScratch\\TryCompile-p3c2p6\\cmTC_6f839.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:00.99
        
      exitCode: 0
...
