prefix=${pcfiledir}/../..
exec_prefix=${prefix}
libdir=${prefix}/lib
includedir=${prefix}/../include
ccompiler=C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe

Name: netCDF
Description: NetCDF Client Library for C
URL: http://www.unidata.ucar.edu/netcdf
Version: 4.8.1
Libs: "-L${libdir}" -lnetcdf
Libs.private:  
Cflags: "-I${includedir}"
Requires.private:  hdf5_hl hdf5 libcurl

