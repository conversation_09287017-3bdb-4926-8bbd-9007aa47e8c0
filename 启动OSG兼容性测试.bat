@echo off
chcp 65001 >nul
echo ========================================
echo OSG接口兼容性测试启动器
echo ========================================
echo.

set TEST_ROOT=%~dp0tests\osg_interface_compatibility

:: 检查测试目录是否存在
if not exist "%TEST_ROOT%" (
    echo [错误] 找不到测试目录: %TEST_ROOT%
    echo 请确认您在项目根目录中运行此脚本
    pause
    exit /b 1
)

echo 测试程序位置:
echo   桌面版: %TEST_ROOT%\redist_desktop\
echo   WebAssembly版: %TEST_ROOT%\redist_wasm\
echo.

echo ========================================
echo 请选择要运行的测试:
echo ========================================
echo.
echo [1] 桌面版测试 (Windows Desktop)
echo [2] WebAssembly版测试 (浏览器)
echo [3] 查看测试指导手册
echo [4] 打开测试目录
echo [5] 退出
echo.

:MENU
set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto DESKTOP_TEST
if "%choice%"=="2" goto WASM_TEST  
if "%choice%"=="3" goto SHOW_MANUAL
if "%choice%"=="4" goto OPEN_DIR
if "%choice%"=="5" goto EXIT

echo 无效选择，请重新输入
goto MENU

:DESKTOP_TEST
echo.
echo ========================================
echo 启动桌面版测试...
echo ========================================
echo.

:: 检查桌面版测试程序
if not exist "%TEST_ROOT%\redist_desktop\osg_compatibility_test.exe" (
    echo [错误] 找不到桌面版测试程序
    echo 请先编译桌面版测试程序
    pause
    goto MENU
)

echo 即将启动桌面版测试程序...
echo 测试将自动配置网络代理并运行所有测试套件
echo.
pause

cd /d "%TEST_ROOT%\redist_desktop"
call run_desktop_tests.bat
cd /d "%~dp0"

echo.
echo 桌面版测试完成，按任意键返回主菜单...
pause >nul
goto MENU

:WASM_TEST
echo.
echo ========================================
echo 启动WebAssembly版测试...
echo ========================================
echo.

:: 检查WebAssembly测试文件
if not exist "%TEST_ROOT%\redist_wasm\osg_compatibility_test.html" (
    echo [错误] 找不到WebAssembly测试文件
    echo 请先编译WebAssembly版测试程序
    pause
    goto MENU
)

echo 即将启动WebAssembly测试...
echo.
echo ⚠️  重要提醒：
echo    请确保浏览器已配置代理 127.0.0.1:10809
echo    否则无法下载地图瓦片数据
echo.
pause

cd /d "%TEST_ROOT%\redist_wasm"
call run_wasm_tests.bat
cd /d "%~dp0"

echo.
echo WebAssembly测试服务器已停止，按任意键返回主菜单...
pause >nul
goto MENU

:SHOW_MANUAL
echo.
echo ========================================
echo 打开测试指导手册...
echo ========================================

if exist "OSG接口兼容性测试指导手册.md" (
    start "" "OSG接口兼容性测试指导手册.md"
    echo 测试指导手册已打开
) else (
    echo [错误] 找不到测试指导手册文件
)

echo.
echo 按任意键返回主菜单...
pause >nul
goto MENU

:OPEN_DIR
echo.
echo ========================================
echo 打开测试目录...
echo ========================================

explorer "%TEST_ROOT%"
echo 测试目录已在文件管理器中打开

echo.
echo 按任意键返回主菜单...
pause >nul
goto MENU

:EXIT
echo.
echo 感谢使用OSG接口兼容性测试工具！
echo.
echo 如有问题，请参考：
echo   - OSG接口兼容性测试指导手册.md
echo   - OSG接口兼容性测试评估报告.md
echo.
pause
exit /b 0
