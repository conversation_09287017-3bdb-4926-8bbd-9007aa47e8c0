x64-windows/
x64-windows/bin/
x64-windows/bin/GeographicLib.dll
x64-windows/bin/GeographicLib.pdb
x64-windows/debug/
x64-windows/debug/bin/
x64-windows/debug/bin/GeographicLib_d.dll
x64-windows/debug/bin/GeographicLib_d.pdb
x64-windows/debug/lib/
x64-windows/debug/lib/GeographicLib_d-i.lib
x64-windows/debug/lib/pkgconfig/
x64-windows/debug/lib/pkgconfig/geographiclib.pc
x64-windows/include/
x64-windows/include/GeographicLib/
x64-windows/include/GeographicLib/Accumulator.hpp
x64-windows/include/GeographicLib/AlbersEqualArea.hpp
x64-windows/include/GeographicLib/AuxAngle.hpp
x64-windows/include/GeographicLib/AuxLatitude.hpp
x64-windows/include/GeographicLib/AzimuthalEquidistant.hpp
x64-windows/include/GeographicLib/CassiniSoldner.hpp
x64-windows/include/GeographicLib/CircularEngine.hpp
x64-windows/include/GeographicLib/Config.h
x64-windows/include/GeographicLib/Constants.hpp
x64-windows/include/GeographicLib/DAuxLatitude.hpp
x64-windows/include/GeographicLib/DMS.hpp
x64-windows/include/GeographicLib/DST.hpp
x64-windows/include/GeographicLib/Ellipsoid.hpp
x64-windows/include/GeographicLib/EllipticFunction.hpp
x64-windows/include/GeographicLib/GARS.hpp
x64-windows/include/GeographicLib/GeoCoords.hpp
x64-windows/include/GeographicLib/Geocentric.hpp
x64-windows/include/GeographicLib/Geodesic.hpp
x64-windows/include/GeographicLib/GeodesicExact.hpp
x64-windows/include/GeographicLib/GeodesicLine.hpp
x64-windows/include/GeographicLib/GeodesicLineExact.hpp
x64-windows/include/GeographicLib/Geohash.hpp
x64-windows/include/GeographicLib/Geoid.hpp
x64-windows/include/GeographicLib/Georef.hpp
x64-windows/include/GeographicLib/Gnomonic.hpp
x64-windows/include/GeographicLib/GravityCircle.hpp
x64-windows/include/GeographicLib/GravityModel.hpp
x64-windows/include/GeographicLib/Intersect.hpp
x64-windows/include/GeographicLib/LambertConformalConic.hpp
x64-windows/include/GeographicLib/LocalCartesian.hpp
x64-windows/include/GeographicLib/MGRS.hpp
x64-windows/include/GeographicLib/MagneticCircle.hpp
x64-windows/include/GeographicLib/MagneticModel.hpp
x64-windows/include/GeographicLib/Math.hpp
x64-windows/include/GeographicLib/NearestNeighbor.hpp
x64-windows/include/GeographicLib/NormalGravity.hpp
x64-windows/include/GeographicLib/OSGB.hpp
x64-windows/include/GeographicLib/PolarStereographic.hpp
x64-windows/include/GeographicLib/PolygonArea.hpp
x64-windows/include/GeographicLib/Rhumb.hpp
x64-windows/include/GeographicLib/SphericalEngine.hpp
x64-windows/include/GeographicLib/SphericalHarmonic.hpp
x64-windows/include/GeographicLib/SphericalHarmonic1.hpp
x64-windows/include/GeographicLib/SphericalHarmonic2.hpp
x64-windows/include/GeographicLib/TransverseMercator.hpp
x64-windows/include/GeographicLib/TransverseMercatorExact.hpp
x64-windows/include/GeographicLib/UTMUPS.hpp
x64-windows/include/GeographicLib/Utility.hpp
x64-windows/lib/
x64-windows/lib/GeographicLib-i.lib
x64-windows/lib/pkgconfig/
x64-windows/lib/pkgconfig/geographiclib.pc
x64-windows/share/
x64-windows/share/geographiclib/
x64-windows/share/geographiclib/copyright
x64-windows/share/geographiclib/geographiclib-config-version.cmake
x64-windows/share/geographiclib/geographiclib-config.cmake
x64-windows/share/geographiclib/geographiclib-targets-debug.cmake
x64-windows/share/geographiclib/geographiclib-targets-release.cmake
x64-windows/share/geographiclib/geographiclib-targets.cmake
x64-windows/share/geographiclib/usage
x64-windows/share/geographiclib/vcpkg.spdx.json
x64-windows/share/geographiclib/vcpkg_abi_info.txt
