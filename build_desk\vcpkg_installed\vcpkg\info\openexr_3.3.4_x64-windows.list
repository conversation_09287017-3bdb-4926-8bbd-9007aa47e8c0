x64-windows/
x64-windows/bin/
x64-windows/bin/Iex-3_3.dll
x64-windows/bin/Iex-3_3.pdb
x64-windows/bin/IlmThread-3_3.dll
x64-windows/bin/IlmThread-3_3.pdb
x64-windows/bin/OpenEXR-3_3.dll
x64-windows/bin/OpenEXR-3_3.pdb
x64-windows/bin/OpenEXRCore-3_3.dll
x64-windows/bin/OpenEXRCore-3_3.pdb
x64-windows/bin/OpenEXRUtil-3_3.dll
x64-windows/bin/OpenEXRUtil-3_3.pdb
x64-windows/debug/
x64-windows/debug/bin/
x64-windows/debug/bin/Iex-3_3_d.dll
x64-windows/debug/bin/Iex-3_3_d.pdb
x64-windows/debug/bin/IlmThread-3_3_d.dll
x64-windows/debug/bin/IlmThread-3_3_d.pdb
x64-windows/debug/bin/OpenEXR-3_3_d.dll
x64-windows/debug/bin/OpenEXR-3_3_d.pdb
x64-windows/debug/bin/OpenEXRCore-3_3_d.dll
x64-windows/debug/bin/OpenEXRCore-3_3_d.pdb
x64-windows/debug/bin/OpenEXRUtil-3_3_d.dll
x64-windows/debug/bin/OpenEXRUtil-3_3_d.pdb
x64-windows/debug/lib/
x64-windows/debug/lib/Iex-3_3_d.lib
x64-windows/debug/lib/IlmThread-3_3_d.lib
x64-windows/debug/lib/OpenEXR-3_3_d.lib
x64-windows/debug/lib/OpenEXRCore-3_3_d.lib
x64-windows/debug/lib/OpenEXRUtil-3_3_d.lib
x64-windows/debug/lib/pkgconfig/
x64-windows/debug/lib/pkgconfig/OpenEXR.pc
x64-windows/include/
x64-windows/include/OpenEXR/
x64-windows/include/OpenEXR/Iex.h
x64-windows/include/OpenEXR/IexBaseExc.h
x64-windows/include/OpenEXR/IexConfig.h
x64-windows/include/OpenEXR/IexErrnoExc.h
x64-windows/include/OpenEXR/IexExport.h
x64-windows/include/OpenEXR/IexForward.h
x64-windows/include/OpenEXR/IexMacros.h
x64-windows/include/OpenEXR/IexMathExc.h
x64-windows/include/OpenEXR/IexMathFloatExc.h
x64-windows/include/OpenEXR/IexMathIeeeExc.h
x64-windows/include/OpenEXR/IexNamespace.h
x64-windows/include/OpenEXR/IexThrowErrnoExc.h
x64-windows/include/OpenEXR/IlmThread.h
x64-windows/include/OpenEXR/IlmThreadConfig.h
x64-windows/include/OpenEXR/IlmThreadExport.h
x64-windows/include/OpenEXR/IlmThreadForward.h
x64-windows/include/OpenEXR/IlmThreadMutex.h
x64-windows/include/OpenEXR/IlmThreadNamespace.h
x64-windows/include/OpenEXR/IlmThreadPool.h
x64-windows/include/OpenEXR/IlmThreadProcessGroup.h
x64-windows/include/OpenEXR/IlmThreadSemaphore.h
x64-windows/include/OpenEXR/ImfAcesFile.h
x64-windows/include/OpenEXR/ImfArray.h
x64-windows/include/OpenEXR/ImfAttribute.h
x64-windows/include/OpenEXR/ImfBoxAttribute.h
x64-windows/include/OpenEXR/ImfCRgbaFile.h
x64-windows/include/OpenEXR/ImfChannelList.h
x64-windows/include/OpenEXR/ImfChannelListAttribute.h
x64-windows/include/OpenEXR/ImfCheckFile.h
x64-windows/include/OpenEXR/ImfChromaticities.h
x64-windows/include/OpenEXR/ImfChromaticitiesAttribute.h
x64-windows/include/OpenEXR/ImfCompositeDeepScanLine.h
x64-windows/include/OpenEXR/ImfCompression.h
x64-windows/include/OpenEXR/ImfCompressionAttribute.h
x64-windows/include/OpenEXR/ImfCompressor.h
x64-windows/include/OpenEXR/ImfContext.h
x64-windows/include/OpenEXR/ImfContextInit.h
x64-windows/include/OpenEXR/ImfConvert.h
x64-windows/include/OpenEXR/ImfDeepCompositing.h
x64-windows/include/OpenEXR/ImfDeepFrameBuffer.h
x64-windows/include/OpenEXR/ImfDeepImage.h
x64-windows/include/OpenEXR/ImfDeepImageChannel.h
x64-windows/include/OpenEXR/ImfDeepImageIO.h
x64-windows/include/OpenEXR/ImfDeepImageLevel.h
x64-windows/include/OpenEXR/ImfDeepImageState.h
x64-windows/include/OpenEXR/ImfDeepImageStateAttribute.h
x64-windows/include/OpenEXR/ImfDeepScanLineInputFile.h
x64-windows/include/OpenEXR/ImfDeepScanLineInputPart.h
x64-windows/include/OpenEXR/ImfDeepScanLineOutputFile.h
x64-windows/include/OpenEXR/ImfDeepScanLineOutputPart.h
x64-windows/include/OpenEXR/ImfDeepTiledInputFile.h
x64-windows/include/OpenEXR/ImfDeepTiledInputPart.h
x64-windows/include/OpenEXR/ImfDeepTiledOutputFile.h
x64-windows/include/OpenEXR/ImfDeepTiledOutputPart.h
x64-windows/include/OpenEXR/ImfDoubleAttribute.h
x64-windows/include/OpenEXR/ImfEnvmap.h
x64-windows/include/OpenEXR/ImfEnvmapAttribute.h
x64-windows/include/OpenEXR/ImfExport.h
x64-windows/include/OpenEXR/ImfFlatImage.h
x64-windows/include/OpenEXR/ImfFlatImageChannel.h
x64-windows/include/OpenEXR/ImfFlatImageIO.h
x64-windows/include/OpenEXR/ImfFlatImageLevel.h
x64-windows/include/OpenEXR/ImfFloatAttribute.h
x64-windows/include/OpenEXR/ImfFloatVectorAttribute.h
x64-windows/include/OpenEXR/ImfForward.h
x64-windows/include/OpenEXR/ImfFrameBuffer.h
x64-windows/include/OpenEXR/ImfFramesPerSecond.h
x64-windows/include/OpenEXR/ImfGenericInputFile.h
x64-windows/include/OpenEXR/ImfGenericOutputFile.h
x64-windows/include/OpenEXR/ImfHeader.h
x64-windows/include/OpenEXR/ImfHuf.h
x64-windows/include/OpenEXR/ImfIDManifest.h
x64-windows/include/OpenEXR/ImfIDManifestAttribute.h
x64-windows/include/OpenEXR/ImfIO.h
x64-windows/include/OpenEXR/ImfImage.h
x64-windows/include/OpenEXR/ImfImageChannel.h
x64-windows/include/OpenEXR/ImfImageChannelRenaming.h
x64-windows/include/OpenEXR/ImfImageDataWindow.h
x64-windows/include/OpenEXR/ImfImageIO.h
x64-windows/include/OpenEXR/ImfImageLevel.h
x64-windows/include/OpenEXR/ImfInputFile.h
x64-windows/include/OpenEXR/ImfInputPart.h
x64-windows/include/OpenEXR/ImfInt64.h
x64-windows/include/OpenEXR/ImfIntAttribute.h
x64-windows/include/OpenEXR/ImfKeyCode.h
x64-windows/include/OpenEXR/ImfKeyCodeAttribute.h
x64-windows/include/OpenEXR/ImfLineOrder.h
x64-windows/include/OpenEXR/ImfLineOrderAttribute.h
x64-windows/include/OpenEXR/ImfLut.h
x64-windows/include/OpenEXR/ImfMatrixAttribute.h
x64-windows/include/OpenEXR/ImfMisc.h
x64-windows/include/OpenEXR/ImfMultiPartInputFile.h
x64-windows/include/OpenEXR/ImfMultiPartOutputFile.h
x64-windows/include/OpenEXR/ImfMultiView.h
x64-windows/include/OpenEXR/ImfName.h
x64-windows/include/OpenEXR/ImfNamespace.h
x64-windows/include/OpenEXR/ImfOpaqueAttribute.h
x64-windows/include/OpenEXR/ImfOutputFile.h
x64-windows/include/OpenEXR/ImfOutputPart.h
x64-windows/include/OpenEXR/ImfPartHelper.h
x64-windows/include/OpenEXR/ImfPartType.h
x64-windows/include/OpenEXR/ImfPixelType.h
x64-windows/include/OpenEXR/ImfPreviewImage.h
x64-windows/include/OpenEXR/ImfPreviewImageAttribute.h
x64-windows/include/OpenEXR/ImfRational.h
x64-windows/include/OpenEXR/ImfRationalAttribute.h
x64-windows/include/OpenEXR/ImfRgba.h
x64-windows/include/OpenEXR/ImfRgbaFile.h
x64-windows/include/OpenEXR/ImfRgbaYca.h
x64-windows/include/OpenEXR/ImfSampleCountChannel.h
x64-windows/include/OpenEXR/ImfStandardAttributes.h
x64-windows/include/OpenEXR/ImfStdIO.h
x64-windows/include/OpenEXR/ImfStringAttribute.h
x64-windows/include/OpenEXR/ImfStringVectorAttribute.h
x64-windows/include/OpenEXR/ImfTestFile.h
x64-windows/include/OpenEXR/ImfThreading.h
x64-windows/include/OpenEXR/ImfTileDescription.h
x64-windows/include/OpenEXR/ImfTileDescriptionAttribute.h
x64-windows/include/OpenEXR/ImfTiledInputFile.h
x64-windows/include/OpenEXR/ImfTiledInputPart.h
x64-windows/include/OpenEXR/ImfTiledOutputFile.h
x64-windows/include/OpenEXR/ImfTiledOutputPart.h
x64-windows/include/OpenEXR/ImfTiledRgbaFile.h
x64-windows/include/OpenEXR/ImfTimeCode.h
x64-windows/include/OpenEXR/ImfTimeCodeAttribute.h
x64-windows/include/OpenEXR/ImfUtilExport.h
x64-windows/include/OpenEXR/ImfVecAttribute.h
x64-windows/include/OpenEXR/ImfVersion.h
x64-windows/include/OpenEXR/ImfWav.h
x64-windows/include/OpenEXR/ImfXdr.h
x64-windows/include/OpenEXR/OpenEXRConfig.h
x64-windows/include/OpenEXR/openexr.h
x64-windows/include/OpenEXR/openexr_attr.h
x64-windows/include/OpenEXR/openexr_base.h
x64-windows/include/OpenEXR/openexr_chunkio.h
x64-windows/include/OpenEXR/openexr_coding.h
x64-windows/include/OpenEXR/openexr_compression.h
x64-windows/include/OpenEXR/openexr_config.h
x64-windows/include/OpenEXR/openexr_context.h
x64-windows/include/OpenEXR/openexr_debug.h
x64-windows/include/OpenEXR/openexr_decode.h
x64-windows/include/OpenEXR/openexr_encode.h
x64-windows/include/OpenEXR/openexr_errors.h
x64-windows/include/OpenEXR/openexr_part.h
x64-windows/include/OpenEXR/openexr_std_attr.h
x64-windows/include/OpenEXR/openexr_version.h
x64-windows/lib/
x64-windows/lib/Iex-3_3.lib
x64-windows/lib/IlmThread-3_3.lib
x64-windows/lib/OpenEXR-3_3.lib
x64-windows/lib/OpenEXRCore-3_3.lib
x64-windows/lib/OpenEXRUtil-3_3.lib
x64-windows/lib/pkgconfig/
x64-windows/lib/pkgconfig/OpenEXR.pc
x64-windows/share/
x64-windows/share/openexr/
x64-windows/share/openexr/OpenEXRConfig.cmake
x64-windows/share/openexr/OpenEXRConfigVersion.cmake
x64-windows/share/openexr/OpenEXRTargets-debug.cmake
x64-windows/share/openexr/OpenEXRTargets-release.cmake
x64-windows/share/openexr/OpenEXRTargets.cmake
x64-windows/share/openexr/copyright
x64-windows/share/openexr/usage
x64-windows/share/openexr/vcpkg.spdx.json
x64-windows/share/openexr/vcpkg_abi_info.txt
