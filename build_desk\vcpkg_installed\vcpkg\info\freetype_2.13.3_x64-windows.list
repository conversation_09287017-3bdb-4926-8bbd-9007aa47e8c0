x64-windows/
x64-windows/bin/
x64-windows/bin/freetype.dll
x64-windows/bin/freetype.pdb
x64-windows/debug/
x64-windows/debug/bin/
x64-windows/debug/bin/freetyped.dll
x64-windows/debug/bin/freetyped.pdb
x64-windows/debug/lib/
x64-windows/debug/lib/freetyped.lib
x64-windows/debug/lib/pkgconfig/
x64-windows/debug/lib/pkgconfig/freetype2.pc
x64-windows/include/
x64-windows/include/freetype/
x64-windows/include/freetype/config/
x64-windows/include/freetype/config/ftconfig.h
x64-windows/include/freetype/config/ftheader.h
x64-windows/include/freetype/config/ftmodule.h
x64-windows/include/freetype/config/ftoption.h
x64-windows/include/freetype/config/ftstdlib.h
x64-windows/include/freetype/config/integer-types.h
x64-windows/include/freetype/config/mac-support.h
x64-windows/include/freetype/config/public-macros.h
x64-windows/include/freetype/freetype.h
x64-windows/include/freetype/ftadvanc.h
x64-windows/include/freetype/ftbbox.h
x64-windows/include/freetype/ftbdf.h
x64-windows/include/freetype/ftbitmap.h
x64-windows/include/freetype/ftbzip2.h
x64-windows/include/freetype/ftcache.h
x64-windows/include/freetype/ftchapters.h
x64-windows/include/freetype/ftcid.h
x64-windows/include/freetype/ftcolor.h
x64-windows/include/freetype/ftdriver.h
x64-windows/include/freetype/fterrdef.h
x64-windows/include/freetype/fterrors.h
x64-windows/include/freetype/ftfntfmt.h
x64-windows/include/freetype/ftgasp.h
x64-windows/include/freetype/ftglyph.h
x64-windows/include/freetype/ftgxval.h
x64-windows/include/freetype/ftgzip.h
x64-windows/include/freetype/ftimage.h
x64-windows/include/freetype/ftincrem.h
x64-windows/include/freetype/ftlcdfil.h
x64-windows/include/freetype/ftlist.h
x64-windows/include/freetype/ftlogging.h
x64-windows/include/freetype/ftlzw.h
x64-windows/include/freetype/ftmac.h
x64-windows/include/freetype/ftmm.h
x64-windows/include/freetype/ftmodapi.h
x64-windows/include/freetype/ftmoderr.h
x64-windows/include/freetype/ftotval.h
x64-windows/include/freetype/ftoutln.h
x64-windows/include/freetype/ftparams.h
x64-windows/include/freetype/ftpfr.h
x64-windows/include/freetype/ftrender.h
x64-windows/include/freetype/ftsizes.h
x64-windows/include/freetype/ftsnames.h
x64-windows/include/freetype/ftstroke.h
x64-windows/include/freetype/ftsynth.h
x64-windows/include/freetype/ftsystem.h
x64-windows/include/freetype/fttrigon.h
x64-windows/include/freetype/fttypes.h
x64-windows/include/freetype/ftwinfnt.h
x64-windows/include/freetype/otsvg.h
x64-windows/include/freetype/t1tables.h
x64-windows/include/freetype/ttnameid.h
x64-windows/include/freetype/tttables.h
x64-windows/include/freetype/tttags.h
x64-windows/include/ft2build.h
x64-windows/lib/
x64-windows/lib/freetype.lib
x64-windows/lib/pkgconfig/
x64-windows/lib/pkgconfig/freetype2.pc
x64-windows/share/
x64-windows/share/freetype/
x64-windows/share/freetype/copyright
x64-windows/share/freetype/freetype-config-version.cmake
x64-windows/share/freetype/freetype-config.cmake
x64-windows/share/freetype/freetype-targets-debug.cmake
x64-windows/share/freetype/freetype-targets-release.cmake
x64-windows/share/freetype/freetype-targets.cmake
x64-windows/share/freetype/usage
x64-windows/share/freetype/vcpkg-cmake-wrapper.cmake
x64-windows/share/freetype/vcpkg.spdx.json
x64-windows/share/freetype/vcpkg_abi_info.txt
