/* Core OSG Rendering Interface Tests
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include "OSGInterfaceTestFramework.h"
#include <osg/Geometry>
#include <osg/Array>
#include <osg/PrimitiveSet>

namespace OSGCompatibilityTest {

    /**
     * 测试基本的Drawable绘制功能
     */
    DECLARE_OSG_TEST(BasicDrawableTest, "BasicDrawable", 
        "Test basic osg::Drawable::drawImplementation functionality")

    /**
     * 测试State的VBO绑定和解绑
     */
    DECLARE_OSG_TEST(StateVBOTest, "StateVBO", 
        "Test osg::State VBO bind/unbind operations")

    /**
     * 测试State的EBO绑定和解绑
     */
    DECLARE_OSG_TEST(StateEBOTest, "StateEBO", 
        "Test osg::State EBO bind/unbind operations")

    /**
     * 测试OpenGL错误检查机制
     */
    DECLARE_OSG_TEST(GLErrorCheckTest, "GLErrorCheck", 
        "Test OpenGL error checking mechanism")

    /**
     * 测试矩阵应用功能
     */
    DECLARE_OSG_TEST(MatrixApplicationTest, "MatrixApplication", 
        "Test projection and modelview matrix application")

    /**
     * 测试StateSet应用
     */
    DECLARE_OSG_TEST(StateSetApplicationTest, "StateSetApplication", 
        "Test StateSet application and state management")

    /**
     * 测试几何体绘制
     */
    DECLARE_OSG_TEST(GeometryDrawTest, "GeometryDraw", 
        "Test geometry drawing with vertex arrays and primitives")

    /**
     * 测试纹理绑定
     */
    DECLARE_OSG_TEST(TextureBindingTest, "TextureBinding", 
        "Test texture binding and management")

    /**
     * 测试着色器程序使用
     */
    DECLARE_OSG_TEST(ShaderProgramTest, "ShaderProgram", 
        "Test shader program compilation and usage")

    /**
     * 测试VAO操作（如果支持）
     */
    DECLARE_OSG_TEST(VAOOperationsTest, "VAOOperations", 
        "Test Vertex Array Object operations")

    /**
     * 创建核心渲染测试套件
     */
    std::unique_ptr<TestSuite> createCoreRenderingTestSuite();

    /**
     * 辅助类：简单的测试用Drawable
     */
    class TestDrawable : public osg::Drawable {
    public:
        TestDrawable();
        
        void drawImplementation(osg::RenderInfo& renderInfo) const override;
        
        // 设置测试参数
        void setTestVBO(bool useVBO) { _useVBO = useVBO; }
        void setTestEBO(bool useEBO) { _useEBO = useEBO; }
        void setTestTexture(bool useTexture) { _useTexture = useTexture; }
        
        // 获取测试结果
        bool getLastDrawSuccess() const { return _lastDrawSuccess; }
        std::string getLastError() const { return _lastError; }
        
    private:
        void setupGeometry();
        void setupTexture();
        
        osg::ref_ptr<osg::Geometry> _geometry;
        osg::ref_ptr<osg::Texture2D> _texture;
        
        bool _useVBO = false;
        bool _useEBO = false;
        bool _useTexture = false;
        
        mutable bool _lastDrawSuccess = false;
        mutable std::string _lastError;
    };

    /**
     * 辅助函数：创建测试用的几何体
     */
    osg::ref_ptr<osg::Geometry> createTestTriangle();
    osg::ref_ptr<osg::Geometry> createTestQuad();
    
    /**
     * 辅助函数：创建测试用的纹理
     */
    osg::ref_ptr<osg::Texture2D> createTestTexture(int width = 64, int height = 64);
    
    /**
     * 辅助函数：创建测试用的着色器程序
     */
    osg::ref_ptr<osg::Program> createTestShaderProgram();

} // namespace OSGCompatibilityTest
