<!doctypehtml><html lang=zh-CN><head><meta charset=utf-8><meta content="width=device-width,initial-scale=1"name=viewport><title>OSG接口兼容性测试 - WebAssembly简化版</title><style>body{font-family:'Segoe UI',Tahoma,Geneva,Verdana,sans-serif;margin:0;padding:20px;background-color:#f5f5f5}.container{max-width:1200px;margin:0 auto;background-color:#fff;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,.1);padding:30px}h1{color:#2c3e50;text-align:center;margin-bottom:30px;border-bottom:3px solid #3498db;padding-bottom:15px}.info-panel{background-color:#e8f4fd;border-left:4px solid #3498db;padding:15px;margin:20px 0;border-radius:4px}.warning-panel{background-color:#fff3cd;border-left:4px solid #ffc107;padding:15px;margin:20px 0;border-radius:4px}.success-panel{background-color:#d4edda;border-left:4px solid #28a745;padding:15px;margin:20px 0;border-radius:4px}.error-panel{background-color:#f8d7da;border-left:4px solid #dc3545;padding:15px;margin:20px 0;border-radius:4px}.console-output{background-color:#2c3e50;color:#ecf0f1;padding:20px;border-radius:4px;font-family:'Courier New',monospace;font-size:14px;line-height:1.4;max-height:400px;overflow-y:auto;white-space:pre-wrap;margin:20px 0}.controls{text-align:center;margin:30px 0}button{background-color:#3498db;color:#fff;border:none;padding:12px 24px;margin:0 10px;border-radius:4px;cursor:pointer;font-size:16px;transition:background-color .3s}button:hover{background-color:#2980b9}button:disabled{background-color:#bdc3c7;cursor:not-allowed}.status{text-align:center;font-size:18px;margin:20px 0;font-weight:700}.loading{color:#f39c12}.ready{color:#27ae60}.error{color:#e74c3c}#canvas{display:none}.test-results{margin-top:30px}.test-item{display:flex;justify-content:space-between;align-items:center;padding:10px;margin:5px 0;border-radius:4px;background-color:#f8f9fa}.test-pass{border-left:4px solid #28a745}.test-fail{border-left:4px solid #dc3545}.test-skip{border-left:4px solid #ffc107}</style></head><body><div class=container><h1>🔬 OSG接口兼容性测试 - WebAssembly简化版</h1><div class=info-panel><h3>📋 测试说明</h3><p>这是一个简化版的OSG接口兼容性测试，专门为WebAssembly环境设计。</p><p>测试内容包括：基础C++功能、内存分配、WebGL上下文创建等。</p></div><div class=warning-panel><h3>⚠️ 重要提醒</h3><p>如果需要访问外部资源（如地图瓦片），请确保浏览器代理设置为：<strong>127.0.0.1:10809</strong></p></div><div class="loading status"id=status>🔄 正在加载WebAssembly模块...</div><div class=controls><button id=runTests onclick=runCompatibilityTests() disabled>🚀 运行兼容性测试</button> <button id=clearOutput onclick=clearConsole()>🗑️ 清空输出</button></div><div class=console-output id=output>等待WebAssembly模块加载完成...</div><canvas height=600 id=canvas width=800></canvas></div><script>let outputElement=document.getElementById("output"),statusElement=document.getElementById("status"),runButton=document.getElementById("runTests");function addOutput(t,e="info"){const n=`[${(new Date).toLocaleTimeString()}] ${t}\n`;outputElement.textContent+=n,outputElement.scrollTop=outputElement.scrollHeight,t.includes("All tests passed")?(statusElement.textContent="✅ 所有测试通过！",statusElement.className="status ready"):t.includes("Some tests failed")&&(statusElement.textContent="❌ 部分测试失败",statusElement.className="status error")}const originalLog=console.log,originalError=console.error;function clearConsole(){outputElement.textContent="",addOutput("控制台已清空")}function runCompatibilityTests(){addOutput("开始运行OSG接口兼容性测试..."),runButton.disabled=!0;try{Module.ccall("main","number",[],[])}catch(t){addOutput("测试执行出错: "+t.message,"error"),console.error("Test execution error:",t)}finally{runButton.disabled=!1}}console.log=function(...t){addOutput(t.join(" "),"info"),originalLog.apply(console,t)},console.error=function(...t){addOutput("ERROR: "+t.join(" "),"error"),originalError.apply(console,t)};var Module={preRun:[],postRun:[],print:function(t){addOutput(t)},printErr:function(t){addOutput("STDERR: "+t,"error")},canvas:function(){var t=document.getElementById("canvas");return t.addEventListener("webglcontextlost",(function(t){alert("WebGL context lost. You will need to reload the page."),t.preventDefault()}),!1),t}(),setStatus:function(t){if(Module.setStatus.last||(Module.setStatus.last={time:Date.now(),text:""}),t!==Module.setStatus.last.text){var e=t.match(/([^(]+)\((\d+(\.\d+)?)\/(\d+)\)/),n=Date.now();e&&n-Module.setStatus.last.time<30||(Module.setStatus.last.time=n,Module.setStatus.last.text=t,t?(statusElement.textContent="🔄 "+t,statusElement.className="status loading"):(statusElement.textContent="✅ WebAssembly模块加载完成",statusElement.className="status ready",runButton.disabled=!1,addOutput("WebAssembly模块加载完成，可以开始测试")))}},totalDependencies:0,monitorRunDependencies:function(t){this.totalDependencies=Math.max(this.totalDependencies,t),Module.setStatus(t?"Preparing... ("+(this.totalDependencies-t)+"/"+this.totalDependencies+")":"All downloads complete.")}};Module.setStatus("正在下载WebAssembly模块..."),window.onerror=function(){Module.setStatus("发生异常，请查看控制台"),statusElement.className="status error"}</script><script async src=osg_simple_test.js></script></body></html>