# OSG Interface Compatibility Test Framework

## 概述

这个测试框架专门用于验证OSG（OpenSceneGraph）接口在不同平台（桌面和WebAssembly）之间的兼容性。通过系统性地测试每个关键的OSG渲染接口，我们可以识别和修复WebAssembly环境下的兼容性问题。

## 背景

在前面的分析中，我们确定了osgEarth的渲染问题根源在于OSG层面的WebGL兼容性。osgEarth完全依赖OSG的渲染管线，因此确保OSG接口在WebAssembly环境下的正确行为是解决问题的关键。

## 测试架构

### 核心组件

1. **OSGInterfaceTestFramework** - 主测试框架类
2. **TestCase** - 测试用例基类
3. **TestSuite** - 测试套件管理
4. **CoreRenderingTests** - 核心渲染接口测试

### 测试覆盖范围

#### 优先级1: 核心渲染接口
- `osg::Drawable::drawImplementation()`
- `osg::State::unbindVertexBufferObject()`
- `osg::State::unbindElementBufferObject()`
- `osg::State::checkGLErrors()`

#### 优先级2: 矩阵和变换
- `osg::State::applyProjectionMatrix()`
- `osg::State::applyModelViewMatrix()`
- Uniform设置相关接口

#### 优先级3: 高级功能
- VAO绑定/解绑
- OpenGL扩展访问
- 纹理管理

## 构建和运行

### 桌面版构建

```bash
# 创建构建目录
mkdir build_desktop
cd build_desktop

# 配置CMake
cmake .. -DCMAKE_BUILD_TYPE=Release

# 编译
make osg_compatibility_test

# 运行测试
./bin/osg_compatibility_test

# 运行特定测试套件
./bin/osg_compatibility_test --suite core-rendering

# 生成详细报告
./bin/osg_compatibility_test --report desktop_report.html --verbose
```

### WebAssembly版构建

```bash
# 设置Emscripten环境
source /path/to/emsdk/emsdk_env.sh

# 创建构建目录
mkdir build_wasm
cd build_wasm

# 配置CMake for Emscripten
emcmake cmake .. -DCMAKE_BUILD_TYPE=Release

# 编译
emmake make osg_compatibility_test

# 运行（需要HTTP服务器）
python3 -m http.server 8080
# 然后在浏览器中访问 http://localhost:8080/osg_compatibility_test.html
```

## 使用方法

### 命令行选项

```bash
osg_compatibility_test [options]

Options:
  --help, -h          显示帮助信息
  --suite <name>      运行指定的测试套件
  --report <file>     生成HTML报告（默认：osg_compatibility_report.html）
  --verbose, -v       启用详细输出
```

### 可用的测试套件

- `core-rendering` - 核心OSG渲染接口测试

### 测试结果解释

测试结果分为以下几种状态：

- **PASS** - 测试通过，接口行为正常
- **FAIL** - 测试失败，发现兼容性问题
- **SKIP** - 测试跳过，通常由于前置条件不满足
- **NOT_SUPPORTED** - 功能不支持，在当前平台不可用

## 添加新测试

### 1. 创建测试用例

```cpp
// 在CoreRenderingTests.h中声明
DECLARE_OSG_TEST(MyNewTest, "MyNewTest", "Description of my test")

// 在CoreRenderingTests.cpp中实现
IMPLEMENT_OSG_TEST(MyNewTest) {
    try {
        // 测试逻辑
        osg::State* state = renderInfo.getState();
        
        // 执行测试操作
        // ...
        
        // 检查结果
        OSG_TEST_ASSERT(condition, "Error message");
        OSG_TEST_ASSERT_GL_NO_ERROR("After operation");
        
        return TestResult::PASS;
        
    } catch (const std::exception& e) {
        return TestResult::FAIL;
    }
}
```

### 2. 添加到测试套件

```cpp
// 在createCoreRenderingTestSuite()中添加
suite->addTest(std::make_unique<MyNewTest>());
```

## 测试报告

测试框架会生成详细的HTML报告，包含：

- 平台信息（操作系统、OpenGL版本、渲染器）
- 测试统计（总数、通过、失败、跳过等）
- 详细的测试结果表格
- 执行时间统计

## 故障排除

### 常见问题

1. **图形上下文创建失败**
   - 确保系统支持OpenGL/WebGL
   - 检查驱动程序是否最新

2. **WebAssembly版本无法运行**
   - 确保浏览器支持WebGL 2.0
   - 检查Emscripten版本兼容性

3. **测试失败率高**
   - 检查OSG版本兼容性
   - 查看详细错误信息定位问题

### 调试技巧

1. 使用`--verbose`选项获取详细输出
2. 检查生成的HTML报告中的错误信息
3. 在浏览器开发者工具中查看WebGL错误

## 扩展测试框架

### 添加新的测试套件

1. 创建新的头文件和实现文件
2. 实现测试用例类
3. 创建测试套件工厂函数
4. 在main.cpp中注册新的测试套件

### 自定义测试断言

可以扩展现有的断言宏或创建新的断言：

```cpp
#define OSG_TEST_ASSERT_TEXTURE_VALID(texture, message) \
    do { \
        if (!texture || !texture->valid()) { \
            return TestResult::FAIL; \
        } \
    } while(0)
```

## 贡献指南

1. 遵循现有的代码风格
2. 为新测试添加适当的文档
3. 确保测试在桌面和WebAssembly环境下都能运行
4. 提交前运行完整的测试套件

## 许可证

MIT License - 详见LICENSE文件
