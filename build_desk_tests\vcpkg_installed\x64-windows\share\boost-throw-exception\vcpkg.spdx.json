{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/boost-throw-exception-x64-windows-1.88.0-d8e20504-a2fe-4b8b-8f48-ad660f81a7b6", "name": "boost-throw-exception:x64-windows@1.88.0 47d10691f9a0168fba526246f016307e29a34e075ca6bac7c4a45807d74c15dd", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-14T04:45:15Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "boost-throw-exception", "SPDXID": "SPDXRef-port", "versionInfo": "1.88.0", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/boost-throw-exception", "homepage": "https://www.boost.org/libs/throw_exception", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Boost throw_exception module", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "boost-throw-exception:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "47d10691f9a0168fba526246f016307e29a34e075ca6bac7c4a45807d74c15dd", "downloadLocation": "NONE", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "boostorg/throw_exception", "downloadLocation": "git+https://github.com/boostorg/throw_exception@boost-1.88.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "519810acfd729e57909180e9b614ca7d9fe5aeea946b8d4ac20e7c60aced6f5d61ae00b552b0115d6a80d41b9ba3c3daf11ffb5f5eb13d7ad9db76d0e017dbee"}]}], "files": [{"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "26deca7391406537b33a9082c2f0d745022ff5b51fc21bd1420d440751d0c721"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "8e3816f1b8ea7c02634aa8503a7b70f5cc7b19156cfecf1701ccee96514a00ee"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}