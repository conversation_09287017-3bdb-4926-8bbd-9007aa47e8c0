// Copyright 2008, Google Inc. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//
//  1. Redistributions of source code must retain the above copyright notice,
//     this list of conditions and the following disclaimer.
//  2. Redistributions in binary form must reproduce the above copyright notice,
//     this list of conditions and the following disclaimer in the documentation
//     and/or other materials provided with the distribution.
//  3. Neither the name of Google Inc. nor the names of its contributors may be
//     used to endorse or promote products derived from this software without
//     specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
// WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
// MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
// EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON> DAMAGES (INCLUDING, BUT NOT LIMITED TO,
// PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
// OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
// WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
// OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF
// ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

#ifndef KML_DOM_KMLDOM_H__
#define KML_DOM_KMLDOM_H__

#include "kml/dom/abstractview.h"
#include "kml/dom/atom.h"
#include "kml/dom/balloonstyle.h"
#include "kml/dom/container.h"
#include "kml/dom/document.h"
#include "kml/dom/element.h"
#include "kml/dom/extendeddata.h"
#include "kml/dom/feature.h"
#include "kml/dom/folder.h"
#include "kml/dom/geometry.h"
#include "kml/dom/iconstyle.h"
#include "kml/dom/kml.h"
#include "kml/dom/labelstyle.h"
#include "kml/dom/link.h"
#include "kml/dom/liststyle.h"
#include "kml/dom/model.h"
#include "kml/dom/networklink.h"
#include "kml/dom/networklinkcontrol.h"
#include "kml/dom/object.h"
#include "kml/dom/overlay.h"
#include "kml/dom/placemark.h"
#include "kml/dom/polystyle.h"
#include "kml/dom/region.h"
#include "kml/dom/schema.h"
#include "kml/dom/snippet.h"
#include "kml/dom/style.h"
#include "kml/dom/stylemap.h"
#include "kml/dom/timeprimitive.h"
#include "kml/dom/gx_timeprimitive.h"
#include "kml/dom/gx_tour.h"
#include "kml/dom/xal.h"
#include "kml/dom/visitor.h"

#endif  // KML_DOM_KMLDOM_H__
