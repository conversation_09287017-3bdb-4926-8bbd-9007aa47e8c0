prefix=${pcfiledir}/../..
exec_prefix=${prefix}
libdir=${prefix}/lib
includedir=${prefix}/../include

Name: absl_flags_reflection
Description: Abseil flags_reflection library
URL: https://abseil.io/
Version: 20250127
Requires: absl_config = 20250127, absl_flags_commandlineflag = 20250127, absl_flags_private_handle_accessor = 20250127, absl_flags_config = 20250127, absl_strings = 20250127, absl_synchronization = 20250127, absl_flat_hash_map = 20250127, absl_no_destructor = 20250127
Libs: "-L${libdir}" -labsl_flags_reflection -ignore:4221
Cflags: "-I${includedir}" /W3 /bigobj /wd4005 /wd4068 /wd4180 /wd4244 /wd4267 /wd4503 /wd4800 /DNOMINMAX /DWIN32_LEAN_AND_MEAN /D_CRT_SECURE_NO_WARNINGS /D_SCL_SECURE_NO_WARNINGS /D_ENABLE_EXTENDED_ALIGNED_STORAGE

