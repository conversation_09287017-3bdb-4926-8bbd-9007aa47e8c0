<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "urn:fontconfig:fonts.dtd">
<fontconfig>
  <description>Load per-user customization files</description>
	<!--
	    Load per-user customization files where stored on XDG Base Directory
	    specification compliant places. it should be usually:
	      $HOME/.config/fontconfig/conf.d
	      $HOME/.config/fontconfig/fonts.conf
	-->
	<include ignore_missing="yes" prefix="xdg">fontconfig/conf.d</include>
	<include ignore_missing="yes" prefix="xdg">fontconfig/fonts.conf</include>
	<!-- the following elements will be removed in the future -->
	<include ignore_missing="yes" deprecated="yes">~/.fonts.conf.d</include>
	<include ignore_missing="yes" deprecated="yes">~/.fonts.conf</include>
</fontconfig>
