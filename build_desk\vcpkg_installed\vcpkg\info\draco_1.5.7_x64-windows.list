x64-windows/
x64-windows/bin/
x64-windows/bin/draco.dll
x64-windows/bin/draco.pdb
x64-windows/debug/
x64-windows/debug/bin/
x64-windows/debug/bin/draco.dll
x64-windows/debug/bin/draco.pdb
x64-windows/debug/lib/
x64-windows/debug/lib/draco.lib
x64-windows/debug/lib/pkgconfig/
x64-windows/debug/lib/pkgconfig/draco.pc
x64-windows/include/
x64-windows/include/draco/
x64-windows/include/draco/animation/
x64-windows/include/draco/animation/keyframe_animation.h
x64-windows/include/draco/animation/keyframe_animation_decoder.h
x64-windows/include/draco/animation/keyframe_animation_encoder.h
x64-windows/include/draco/attributes/
x64-windows/include/draco/attributes/attribute_octahedron_transform.h
x64-windows/include/draco/attributes/attribute_quantization_transform.h
x64-windows/include/draco/attributes/attribute_transform.h
x64-windows/include/draco/attributes/attribute_transform_data.h
x64-windows/include/draco/attributes/attribute_transform_type.h
x64-windows/include/draco/attributes/geometry_attribute.h
x64-windows/include/draco/attributes/geometry_indices.h
x64-windows/include/draco/attributes/point_attribute.h
x64-windows/include/draco/compression/
x64-windows/include/draco/compression/attributes/
x64-windows/include/draco/compression/attributes/attributes_decoder.h
x64-windows/include/draco/compression/attributes/attributes_decoder_interface.h
x64-windows/include/draco/compression/attributes/attributes_encoder.h
x64-windows/include/draco/compression/attributes/kd_tree_attributes_decoder.h
x64-windows/include/draco/compression/attributes/kd_tree_attributes_encoder.h
x64-windows/include/draco/compression/attributes/kd_tree_attributes_shared.h
x64-windows/include/draco/compression/attributes/linear_sequencer.h
x64-windows/include/draco/compression/attributes/mesh_attribute_indices_encoding_data.h
x64-windows/include/draco/compression/attributes/normal_compression_utils.h
x64-windows/include/draco/compression/attributes/point_d_vector.h
x64-windows/include/draco/compression/attributes/points_sequencer.h
x64-windows/include/draco/compression/attributes/prediction_schemes/
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_constrained_multi_parallelogram_decoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_constrained_multi_parallelogram_encoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_constrained_multi_parallelogram_shared.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_data.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_decoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_encoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_geometric_normal_decoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_geometric_normal_encoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_geometric_normal_predictor_area.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_geometric_normal_predictor_base.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_multi_parallelogram_decoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_multi_parallelogram_encoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_parallelogram_encoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_parallelogram_shared.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_tex_coords_decoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_tex_coords_encoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_tex_coords_portable_decoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_tex_coords_portable_encoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/mesh_prediction_scheme_tex_coords_portable_predictor.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_decoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_decoder_factory.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_decoder_interface.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_decoding_transform.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_delta_decoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_delta_encoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_encoder.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_encoder_factory.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_encoder_interface.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_encoding_transform.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_factory.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_interface.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_normal_octahedron_canonicalized_decoding_transform.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_normal_octahedron_canonicalized_encoding_transform.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_normal_octahedron_canonicalized_transform_base.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_normal_octahedron_decoding_transform.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_normal_octahedron_encoding_transform.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_normal_octahedron_transform_base.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_wrap_decoding_transform.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_wrap_encoding_transform.h
x64-windows/include/draco/compression/attributes/prediction_schemes/prediction_scheme_wrap_transform_base.h
x64-windows/include/draco/compression/attributes/sequential_attribute_decoder.h
x64-windows/include/draco/compression/attributes/sequential_attribute_decoders_controller.h
x64-windows/include/draco/compression/attributes/sequential_attribute_encoder.h
x64-windows/include/draco/compression/attributes/sequential_attribute_encoders_controller.h
x64-windows/include/draco/compression/attributes/sequential_integer_attribute_decoder.h
x64-windows/include/draco/compression/attributes/sequential_integer_attribute_encoder.h
x64-windows/include/draco/compression/attributes/sequential_normal_attribute_decoder.h
x64-windows/include/draco/compression/attributes/sequential_normal_attribute_encoder.h
x64-windows/include/draco/compression/attributes/sequential_quantization_attribute_decoder.h
x64-windows/include/draco/compression/attributes/sequential_quantization_attribute_encoder.h
x64-windows/include/draco/compression/bit_coders/
x64-windows/include/draco/compression/bit_coders/adaptive_rans_bit_coding_shared.h
x64-windows/include/draco/compression/bit_coders/adaptive_rans_bit_decoder.h
x64-windows/include/draco/compression/bit_coders/adaptive_rans_bit_encoder.h
x64-windows/include/draco/compression/bit_coders/direct_bit_decoder.h
x64-windows/include/draco/compression/bit_coders/direct_bit_encoder.h
x64-windows/include/draco/compression/bit_coders/folded_integer_bit_decoder.h
x64-windows/include/draco/compression/bit_coders/folded_integer_bit_encoder.h
x64-windows/include/draco/compression/bit_coders/rans_bit_decoder.h
x64-windows/include/draco/compression/bit_coders/rans_bit_encoder.h
x64-windows/include/draco/compression/bit_coders/symbol_bit_decoder.h
x64-windows/include/draco/compression/bit_coders/symbol_bit_encoder.h
x64-windows/include/draco/compression/config/
x64-windows/include/draco/compression/config/compression_shared.h
x64-windows/include/draco/compression/config/decoder_options.h
x64-windows/include/draco/compression/config/draco_options.h
x64-windows/include/draco/compression/config/encoder_options.h
x64-windows/include/draco/compression/config/encoding_features.h
x64-windows/include/draco/compression/decode.h
x64-windows/include/draco/compression/draco_compression_options.h
x64-windows/include/draco/compression/encode.h
x64-windows/include/draco/compression/encode_base.h
x64-windows/include/draco/compression/entropy/
x64-windows/include/draco/compression/entropy/ans.h
x64-windows/include/draco/compression/entropy/rans_symbol_coding.h
x64-windows/include/draco/compression/entropy/rans_symbol_decoder.h
x64-windows/include/draco/compression/entropy/rans_symbol_encoder.h
x64-windows/include/draco/compression/entropy/shannon_entropy.h
x64-windows/include/draco/compression/entropy/symbol_decoding.h
x64-windows/include/draco/compression/entropy/symbol_encoding.h
x64-windows/include/draco/compression/expert_encode.h
x64-windows/include/draco/compression/mesh/
x64-windows/include/draco/compression/mesh/mesh_decoder.h
x64-windows/include/draco/compression/mesh/mesh_edgebreaker_decoder.h
x64-windows/include/draco/compression/mesh/mesh_edgebreaker_decoder_impl.h
x64-windows/include/draco/compression/mesh/mesh_edgebreaker_decoder_impl_interface.h
x64-windows/include/draco/compression/mesh/mesh_edgebreaker_encoder.h
x64-windows/include/draco/compression/mesh/mesh_edgebreaker_encoder_impl.h
x64-windows/include/draco/compression/mesh/mesh_edgebreaker_encoder_impl_interface.h
x64-windows/include/draco/compression/mesh/mesh_edgebreaker_shared.h
x64-windows/include/draco/compression/mesh/mesh_edgebreaker_traversal_decoder.h
x64-windows/include/draco/compression/mesh/mesh_edgebreaker_traversal_encoder.h
x64-windows/include/draco/compression/mesh/mesh_edgebreaker_traversal_predictive_decoder.h
x64-windows/include/draco/compression/mesh/mesh_edgebreaker_traversal_predictive_encoder.h
x64-windows/include/draco/compression/mesh/mesh_edgebreaker_traversal_valence_decoder.h
x64-windows/include/draco/compression/mesh/mesh_edgebreaker_traversal_valence_encoder.h
x64-windows/include/draco/compression/mesh/mesh_encoder.h
x64-windows/include/draco/compression/mesh/mesh_sequential_decoder.h
x64-windows/include/draco/compression/mesh/mesh_sequential_encoder.h
x64-windows/include/draco/compression/mesh/traverser/
x64-windows/include/draco/compression/mesh/traverser/depth_first_traverser.h
x64-windows/include/draco/compression/mesh/traverser/max_prediction_degree_traverser.h
x64-windows/include/draco/compression/mesh/traverser/mesh_attribute_indices_encoding_observer.h
x64-windows/include/draco/compression/mesh/traverser/mesh_traversal_sequencer.h
x64-windows/include/draco/compression/mesh/traverser/traverser_base.h
x64-windows/include/draco/compression/point_cloud/
x64-windows/include/draco/compression/point_cloud/algorithms/
x64-windows/include/draco/compression/point_cloud/algorithms/dynamic_integer_points_kd_tree_decoder.h
x64-windows/include/draco/compression/point_cloud/algorithms/dynamic_integer_points_kd_tree_encoder.h
x64-windows/include/draco/compression/point_cloud/algorithms/float_points_tree_decoder.h
x64-windows/include/draco/compression/point_cloud/algorithms/float_points_tree_encoder.h
x64-windows/include/draco/compression/point_cloud/algorithms/point_cloud_compression_method.h
x64-windows/include/draco/compression/point_cloud/algorithms/point_cloud_types.h
x64-windows/include/draco/compression/point_cloud/algorithms/quantize_points_3.h
x64-windows/include/draco/compression/point_cloud/algorithms/queuing_policy.h
x64-windows/include/draco/compression/point_cloud/point_cloud_decoder.h
x64-windows/include/draco/compression/point_cloud/point_cloud_encoder.h
x64-windows/include/draco/compression/point_cloud/point_cloud_kd_tree_decoder.h
x64-windows/include/draco/compression/point_cloud/point_cloud_kd_tree_encoder.h
x64-windows/include/draco/compression/point_cloud/point_cloud_sequential_decoder.h
x64-windows/include/draco/compression/point_cloud/point_cloud_sequential_encoder.h
x64-windows/include/draco/core/
x64-windows/include/draco/core/bit_utils.h
x64-windows/include/draco/core/bounding_box.h
x64-windows/include/draco/core/constants.h
x64-windows/include/draco/core/cycle_timer.h
x64-windows/include/draco/core/data_buffer.h
x64-windows/include/draco/core/decoder_buffer.h
x64-windows/include/draco/core/divide.h
x64-windows/include/draco/core/draco_index_type.h
x64-windows/include/draco/core/draco_index_type_vector.h
x64-windows/include/draco/core/draco_types.h
x64-windows/include/draco/core/draco_version.h
x64-windows/include/draco/core/encoder_buffer.h
x64-windows/include/draco/core/hash_utils.h
x64-windows/include/draco/core/macros.h
x64-windows/include/draco/core/math_utils.h
x64-windows/include/draco/core/options.h
x64-windows/include/draco/core/quantization_utils.h
x64-windows/include/draco/core/status.h
x64-windows/include/draco/core/status_or.h
x64-windows/include/draco/core/varint_decoding.h
x64-windows/include/draco/core/varint_encoding.h
x64-windows/include/draco/core/vector_d.h
x64-windows/include/draco/draco_features.h
x64-windows/include/draco/io/
x64-windows/include/draco/io/file_reader_factory.h
x64-windows/include/draco/io/file_reader_interface.h
x64-windows/include/draco/io/file_utils.h
x64-windows/include/draco/io/file_writer_factory.h
x64-windows/include/draco/io/file_writer_interface.h
x64-windows/include/draco/io/file_writer_utils.h
x64-windows/include/draco/io/mesh_io.h
x64-windows/include/draco/io/obj_decoder.h
x64-windows/include/draco/io/obj_encoder.h
x64-windows/include/draco/io/parser_utils.h
x64-windows/include/draco/io/ply_decoder.h
x64-windows/include/draco/io/ply_encoder.h
x64-windows/include/draco/io/ply_property_reader.h
x64-windows/include/draco/io/ply_property_writer.h
x64-windows/include/draco/io/ply_reader.h
x64-windows/include/draco/io/point_cloud_io.h
x64-windows/include/draco/io/stdio_file_reader.h
x64-windows/include/draco/io/stdio_file_writer.h
x64-windows/include/draco/io/stl_decoder.h
x64-windows/include/draco/io/stl_encoder.h
x64-windows/include/draco/mesh/
x64-windows/include/draco/mesh/corner_table.h
x64-windows/include/draco/mesh/corner_table_iterators.h
x64-windows/include/draco/mesh/mesh.h
x64-windows/include/draco/mesh/mesh_are_equivalent.h
x64-windows/include/draco/mesh/mesh_attribute_corner_table.h
x64-windows/include/draco/mesh/mesh_cleanup.h
x64-windows/include/draco/mesh/mesh_features.h
x64-windows/include/draco/mesh/mesh_indices.h
x64-windows/include/draco/mesh/mesh_misc_functions.h
x64-windows/include/draco/mesh/mesh_stripifier.h
x64-windows/include/draco/mesh/triangle_soup_mesh_builder.h
x64-windows/include/draco/mesh/valence_cache.h
x64-windows/include/draco/metadata/
x64-windows/include/draco/metadata/geometry_metadata.h
x64-windows/include/draco/metadata/metadata.h
x64-windows/include/draco/metadata/metadata_decoder.h
x64-windows/include/draco/metadata/metadata_encoder.h
x64-windows/include/draco/metadata/property_attribute.h
x64-windows/include/draco/metadata/property_table.h
x64-windows/include/draco/metadata/structural_metadata.h
x64-windows/include/draco/metadata/structural_metadata_schema.h
x64-windows/include/draco/point_cloud/
x64-windows/include/draco/point_cloud/point_cloud.h
x64-windows/include/draco/point_cloud/point_cloud_builder.h
x64-windows/lib/
x64-windows/lib/draco.lib
x64-windows/lib/pkgconfig/
x64-windows/lib/pkgconfig/draco.pc
x64-windows/share/
x64-windows/share/draco/
x64-windows/share/draco/copyright
x64-windows/share/draco/draco-config-version.cmake
x64-windows/share/draco/draco-config.cmake
x64-windows/share/draco/draco-targets-debug.cmake
x64-windows/share/draco/draco-targets-release.cmake
x64-windows/share/draco/draco-targets.cmake
x64-windows/share/draco/vcpkg.spdx.json
x64-windows/share/draco/vcpkg_abi_info.txt
x64-windows/tools/
x64-windows/tools/draco/
x64-windows/tools/draco/draco.dll
x64-windows/tools/draco/draco_decoder.exe
x64-windows/tools/draco/draco_encoder.exe
