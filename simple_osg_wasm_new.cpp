// 简单的 OSG WebAssembly 测试程序 - 避免 osgText 依赖
#include <iostream>
#include <memory>

// OSG 核心头文件
#include <osg/ref_ptr>
#include <osg/Group>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/Shape>
#include <osg/ShapeDrawable>
#include <osg/Material>
#include <osg/StateSet>

// OSG 查看器 - 不使用事件处理器
#include <osgViewer/Viewer>
#include <osgGA/TrackballManipulator>

// osgEarth 渲染引擎
#include <osgEarth/RenderingEngine.h>

// SDL2 和 Emscripten
#include <SDL2/SDL.h>
#include <emscripten.h>
#include <emscripten/html5.h>

using namespace osg;

// Emscripten GraphicsContext实现
class EmscriptenGraphicsContext : public osg::GraphicsContext
{
public:
    EmscriptenGraphicsContext(Traits *traits) : osg::GraphicsContext()
    {
        _traits = traits;
        _valid = true;
        _realized = false;

        setState(new osg::State);
        getState()->setGraphicsContext(this);
    }

    virtual bool valid() const { return _valid; }
    virtual bool realizeImplementation()
    {
        _realized = true;
        return true;
    }
    virtual bool isRealizedImplementation() const { return _realized; }
    virtual void closeImplementation() { _realized = false; }
    virtual bool makeCurrentImplementation() { return true; }
    virtual bool makeContextCurrentImplementation(GraphicsContext *readContext) { return true; }
    virtual bool releaseContextImplementation() { return true; }
    virtual void swapBuffersImplementation()
    {
        if (s_window)
            SDL_GL_SwapWindow(s_window);
    }
    virtual void bindPBufferToTextureImplementation(GLenum buffer) {}
    virtual void resizedImplementation(int x, int y, int width, int height) {}

    static void setWindow(SDL_Window *window) { s_window = window; }

private:
    bool _valid;
    bool _realized;
    static SDL_Window *s_window;
};

SDL_Window *EmscriptenGraphicsContext::s_window = nullptr;

// 全局变量
osg::ref_ptr<osgViewer::Viewer> g_viewer;
std::unique_ptr<osgEarth::IRenderingEngine> g_renderingEngine;
SDL_Window *g_window = nullptr;
SDL_GLContext g_glContext = nullptr;

// 创建一个简单的彩色球体
osg::ref_ptr<osg::Node> createColoredSphere()
{
    osg::ref_ptr<osg::Sphere> sphere = new osg::Sphere(osg::Vec3(0, 0, 0), 1.0f);
    osg::ref_ptr<osg::ShapeDrawable> drawable = new osg::ShapeDrawable(sphere.get());

    osg::ref_ptr<osg::Material> material = new osg::Material();
    material->setDiffuse(osg::Material::FRONT_AND_BACK, osg::Vec4(0.2f, 0.5f, 1.0f, 1.0f));
    material->setSpecular(osg::Material::FRONT_AND_BACK, osg::Vec4(0.8f, 0.8f, 0.8f, 1.0f));
    material->setShininess(osg::Material::FRONT_AND_BACK, 64.0f);

    osg::ref_ptr<osg::StateSet> stateSet = drawable->getOrCreateStateSet();
    stateSet->setAttributeAndModes(material.get(), osg::StateAttribute::ON);

    osg::ref_ptr<osg::Geode> geode = new osg::Geode();
    geode->addDrawable(drawable.get());

    return geode.get();
}

// 创建场景
osg::ref_ptr<osg::Node> createScene()
{
    osg::ref_ptr<osg::Group> root = new osg::Group();

    osg::ref_ptr<osg::Node> sphere = createColoredSphere();
    root->addChild(sphere.get());

    std::cout << "[INFO] Simple OSG scene created" << std::endl;

    return root.get();
}

// 初始化SDL2
bool initializeSDL2()
{
    std::cout << "[INFO] Initializing SDL2..." << std::endl;

    if (SDL_Init(SDL_INIT_VIDEO) < 0)
    {
        std::cout << "[ERROR] SDL initialization failed: " << SDL_GetError() << std::endl;
        return false;
    }

    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 3);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_PROFILE_MASK, SDL_GL_CONTEXT_PROFILE_ES);
    SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
    SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);

    g_window = SDL_CreateWindow(
        "Simple OSG WebAssembly Test",
        SDL_WINDOWPOS_CENTERED,
        SDL_WINDOWPOS_CENTERED,
        1024, 768,
        SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN);

    if (!g_window)
    {
        std::cout << "[ERROR] Window creation failed: " << SDL_GetError() << std::endl;
        return false;
    }

    g_glContext = SDL_GL_CreateContext(g_window);
    if (!g_glContext)
    {
        std::cout << "[ERROR] OpenGL context creation failed: " << SDL_GetError() << std::endl;
        return false;
    }

    SDL_GL_MakeCurrent(g_window, g_glContext);
    EmscriptenGraphicsContext::setWindow(g_window);

    std::cout << "[INFO] SDL2 initialized successfully" << std::endl;
    return true;
}

// 初始化OSG查看器
bool initializeOSGViewer()
{
    std::cout << "[INFO] Initializing OSG Viewer..." << std::endl;

    g_viewer = new osgViewer::Viewer();

    osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits();
    traits->x = 0;
    traits->y = 0;
    traits->width = 1024;
    traits->height = 768;
    traits->windowDecoration = false;
    traits->doubleBuffer = true;
    traits->sharedContext = 0;
    traits->pbuffer = false;

    osg::ref_ptr<EmscriptenGraphicsContext> gc = new EmscriptenGraphicsContext(traits.get());

    if (gc.valid())
    {
        g_viewer->getCamera()->setGraphicsContext(gc.get());
        g_viewer->getCamera()->setViewport(new osg::Viewport(0, 0, traits->width, traits->height));
        g_viewer->getCamera()->setProjectionMatrixAsPerspective(30.0f, (double)traits->width / (double)traits->height, 1.0f, 100.0f);

        std::cout << "[INFO] Graphics context created successfully" << std::endl;
    }
    else
    {
        std::cerr << "[ERROR] Failed to create graphics context" << std::endl;
        return false;
    }

    osg::ref_ptr<osgGA::TrackballManipulator> manipulator = new osgGA::TrackballManipulator();
    manipulator->setDistance(5.0);
    g_viewer->setCameraManipulator(manipulator.get());

    // 不添加任何事件处理器以避免 osgText 依赖

    std::cout << "[INFO] OSG Viewer initialized successfully" << std::endl;
    return true;
}

// 主循环函数
void mainLoop()
{
    if (g_viewer.valid() && !g_viewer->done())
    {
        g_viewer->frame();
    }
}

int main()
{
    std::cout << "[INFO] Starting Simple OSG WebAssembly Test with multithreading support..." << std::endl;

    // 创建渲染引擎（使用工厂模式）
    g_renderingEngine = osgEarth::RenderingEngineFactory::createBest();
    if (!g_renderingEngine)
    {
        std::cout << "[ERROR] 无法创建渲染引擎" << std::endl;
        return -1;
    }

    if (!g_renderingEngine->initialize())
    {
        std::cout << "[ERROR] 渲染引擎初始化失败" << std::endl;
        return -1;
    }

    std::cout << "[INFO] ✅ 渲染引擎就绪: " << g_renderingEngine->getTypeName() << std::endl;

    if (!initializeSDL2())
    {
        std::cout << "[ERROR] Failed to initialize SDL2" << std::endl;
        return -1;
    }

    if (!initializeOSGViewer())
    {
        std::cout << "[ERROR] Failed to initialize OSG Viewer" << std::endl;
        return -1;
    }

    osg::ref_ptr<osg::Node> scene = createScene();
    if (!scene.valid())
    {
        std::cout << "[ERROR] Failed to create scene" << std::endl;
        return -1;
    }

    g_viewer->setSceneData(scene.get());
    g_viewer->realize();

    std::cout << "[INFO] Starting main loop..." << std::endl;
    emscripten_set_main_loop(mainLoop, 0, 1);

    return 0;
}
