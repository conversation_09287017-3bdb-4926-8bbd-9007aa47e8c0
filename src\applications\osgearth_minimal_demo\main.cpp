/* osgEarth Minimal WebAssembly Demo
 * 基于最小化osgEarth库的WebAssembly演示应用
 * 用于验证WebAssembly构建和基本3D功能
 */

#include "../../osgEarth_minimal/Common.h"
#include <osgEarth/RenderingEngine.h>

#ifdef __EMSCRIPTEN__
#include <emscripten.h>
#include <emscripten/html5.h>
#endif

#include <iostream>

using namespace osgEarth;

// 全局变量
osg::ref_ptr<osgViewer::Viewer> g_viewer;
std::unique_ptr<IRenderingEngine> g_renderingEngine;
static int frame_count = 0;

// WebAssembly导出函数声明
extern "C"
{
    void initialize_scene();
    void handle_mouse_down(int x, int y, int button);
    void handle_mouse_up(int x, int y, int button);
    void handle_mouse_drag(int deltaX, int deltaY);
    void handle_wheel(float delta);
    void handle_resize(int width, int height);
    void reset_view();
    void go_home();
    void goto_location(double lat, double lon);
    void change_layer(const char *layer);
    void set_opacity(float opacity);
    void render_frame();
}

/**
 * 初始化3D场景
 */
void initialize_scene()
{
    OE_INFO("[Minimal Demo] 初始化最小化osgEarth场景...");

    try
    {
        // 输出版本信息
        OE_INFO(std::string("[Minimal Demo] osgEarth版本: ") + getVersion());
        OE_INFO(std::string("[Minimal Demo] 库名称: ") + getLibraryName());

        // 创建渲染引擎（使用工厂模式）
        g_renderingEngine = RenderingEngineFactory::createBest();
        if (!g_renderingEngine)
        {
            OE_ERROR("[Minimal Demo] ❌ 无法创建渲染引擎");
            return;
        }

        if (!g_renderingEngine->initialize())
        {
            OE_ERROR("[Minimal Demo] ❌ 渲染引擎初始化失败");
            return;
        }

        OE_INFO(std::string("[Minimal Demo] ✅ 渲染引擎就绪: ") + g_renderingEngine->getTypeName());

        // 创建查看器
        g_viewer = new osgViewer::Viewer();

        // 创建简单的场景根节点
        osg::ref_ptr<osg::Group> root = new osg::Group();
        g_viewer->setSceneData(root.get());

        // 设置相机操作器
        g_viewer->setCameraManipulator(new osgGA::TrackballManipulator());

        // 实现视图
        g_viewer->realize();

        OE_INFO("[Minimal Demo] ✅ 最小化场景初始化成功");
    }
    catch (const std::exception &e)
    {
        OE_ERROR(std::string("[Minimal Demo] ❌ 场景初始化失败: ") + e.what());
    }
}

/**
 * 渲染帧
 */
void render_frame()
{
    if (g_viewer.valid())
    {
        g_viewer->frame();
        frame_count++;

        if (frame_count % 60 == 0)
        {
            OE_DEBUG(std::string("[Minimal Demo] 渲染帧: ") + std::to_string(frame_count));
        }
    }
}

/**
 * 鼠标按下事件
 */
void handle_mouse_down(int x, int y, int button)
{
    if (g_viewer.valid())
    {
        OE_DEBUG(std::string("[Minimal Demo] 鼠标按下: (") + std::to_string(x) + ", " + std::to_string(y) + "), 按钮: " + std::to_string(button));
    }
}

/**
 * 鼠标释放事件
 */
void handle_mouse_up(int x, int y, int button)
{
    if (g_viewer.valid())
    {
        OE_DEBUG(std::string("[Minimal Demo] 鼠标释放: (") + std::to_string(x) + ", " + std::to_string(y) + "), 按钮: " + std::to_string(button));
    }
}

/**
 * 鼠标拖拽事件
 */
void handle_mouse_drag(int deltaX, int deltaY)
{
    if (g_viewer.valid())
    {
        OE_DEBUG(std::string("[Minimal Demo] 鼠标拖拽: (") + std::to_string(deltaX) + ", " + std::to_string(deltaY) + ")");
    }
}

/**
 * 鼠标滚轮事件
 */
void handle_wheel(float delta)
{
    if (g_viewer.valid())
    {
        OE_DEBUG(std::string("[Minimal Demo] 滚轮: ") + std::to_string(delta));
    }
}

/**
 * 窗口大小改变事件
 */
void handle_resize(int width, int height)
{
    if (g_viewer.valid())
    {
        OE_INFO(std::string("[Minimal Demo] 窗口大小改变: ") + std::to_string(width) + "x" + std::to_string(height));
    }
}

/**
 * 重置视图
 */
void reset_view()
{
    if (g_viewer.valid())
    {
        g_viewer->home();
        OE_INFO("[Minimal Demo] 重置视图");
    }
}

/**
 * 回到首页视图
 */
void go_home()
{
    if (g_viewer.valid())
    {
        g_viewer->home();
        OE_INFO("[Minimal Demo] 回到首页视图");
    }
}

/**
 * 跳转到指定位置
 */
void goto_location(double lat, double lon)
{
    if (g_viewer.valid())
    {
        OE_INFO(std::string("[Minimal Demo] 跳转到位置: (") + std::to_string(lat) + ", " + std::to_string(lon) + ")");
        // TODO: 实现位置跳转（需要完整的地图功能）
    }
}

/**
 * 改变图层
 */
void change_layer(const char *layer)
{
    if (g_viewer.valid())
    {
        OE_INFO(std::string("[Minimal Demo] 改变图层: ") + layer);
        // TODO: 实现图层切换（需要完整的地图功能）
    }
}

/**
 * 设置透明度
 */
void set_opacity(float opacity)
{
    if (g_viewer.valid())
    {
        OE_INFO(std::string("[Minimal Demo] 设置透明度: ") + std::to_string(opacity));
        // TODO: 实现透明度设置（需要完整的地图功能）
    }
}

#ifdef __EMSCRIPTEN__
/**
 * Emscripten主循环函数
 */
void main_loop()
{
    render_frame();
}
#endif

/**
 * 主函数
 */
int main(int argc, char **argv)
{
    OE_INFO("=== osgEarth Minimal WebAssembly Demo ===");
    OE_INFO("启动最小化WebAssembly应用...");

    // 初始化场景
    initialize_scene();

#ifdef __EMSCRIPTEN__
    OE_INFO("设置WebAssembly主循环...");
    emscripten_set_main_loop(main_loop, 60, 1);
#else
    OE_INFO("运行桌面模式...");
    if (g_viewer.valid())
    {
        return g_viewer->run();
    }
#endif

    OE_INFO("应用程序初始化完成！");
    return 0;
}
