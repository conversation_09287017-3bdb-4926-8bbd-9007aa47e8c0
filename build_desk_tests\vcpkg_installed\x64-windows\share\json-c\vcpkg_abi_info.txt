cmake 3.30.1
features core
portfile.cmake 6cf58e85f5ddaa1485afc3c0e8fbe485779252fc3e6bdedad5152eaec87eb257
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.2
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
vcpkg-cmake a0dc1d028307cd46ec7e45b561314f30e28ec952c29f9749d9aac9bdf5fed759
vcpkg-cmake-config 472514a630e790ecc84c4109c9d53da233d4ffd78017b825f9117c89e190f16e
vcpkg.json 1be31b9fb135a8a5665412a79996f29da791da0cf364722da49852f5b28da535
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
