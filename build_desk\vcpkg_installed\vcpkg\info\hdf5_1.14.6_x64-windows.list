x64-windows/
x64-windows/bin/
x64-windows/bin/hdf5.dll
x64-windows/bin/hdf5.pdb
x64-windows/bin/hdf5_cpp.dll
x64-windows/bin/hdf5_cpp.pdb
x64-windows/bin/hdf5_hl.dll
x64-windows/bin/hdf5_hl.pdb
x64-windows/bin/hdf5_hl_cpp.dll
x64-windows/bin/hdf5_hl_cpp.pdb
x64-windows/debug/
x64-windows/debug/bin/
x64-windows/debug/bin/hdf5_D.dll
x64-windows/debug/bin/hdf5_D.pdb
x64-windows/debug/bin/hdf5_cpp_D.dll
x64-windows/debug/bin/hdf5_cpp_D.pdb
x64-windows/debug/bin/hdf5_hl_D.dll
x64-windows/debug/bin/hdf5_hl_D.pdb
x64-windows/debug/bin/hdf5_hl_cpp_D.dll
x64-windows/debug/bin/hdf5_hl_cpp_D.pdb
x64-windows/debug/lib/
x64-windows/debug/lib/hdf5_D.lib
x64-windows/debug/lib/hdf5_cpp_D.lib
x64-windows/debug/lib/hdf5_hl_D.lib
x64-windows/debug/lib/hdf5_hl_cpp_D.lib
x64-windows/debug/lib/libhdf5.settings
x64-windows/debug/lib/pkgconfig/
x64-windows/debug/lib/pkgconfig/hdf5.pc
x64-windows/debug/lib/pkgconfig/hdf5_cpp.pc
x64-windows/debug/lib/pkgconfig/hdf5_hl.pc
x64-windows/debug/lib/pkgconfig/hdf5_hl_cpp.pc
x64-windows/include/
x64-windows/include/H5ACpublic.h
x64-windows/include/H5AbstractDs.h
x64-windows/include/H5Alltypes.h
x64-windows/include/H5Apublic.h
x64-windows/include/H5ArrayType.h
x64-windows/include/H5AtomType.h
x64-windows/include/H5Attribute.h
x64-windows/include/H5Classes.h
x64-windows/include/H5CommonFG.h
x64-windows/include/H5CompType.h
x64-windows/include/H5Cpp.h
x64-windows/include/H5CppDoc.h
x64-windows/include/H5Cpublic.h
x64-windows/include/H5DOpublic.h
x64-windows/include/H5DSpublic.h
x64-windows/include/H5DaccProp.h
x64-windows/include/H5DataSet.h
x64-windows/include/H5DataSpace.h
x64-windows/include/H5DataType.h
x64-windows/include/H5DcreatProp.h
x64-windows/include/H5Dpublic.h
x64-windows/include/H5DxferProp.h
x64-windows/include/H5ESdevelop.h
x64-windows/include/H5ESpublic.h
x64-windows/include/H5EnumType.h
x64-windows/include/H5Epubgen.h
x64-windows/include/H5Epublic.h
x64-windows/include/H5Exception.h
x64-windows/include/H5FDcore.h
x64-windows/include/H5FDdevelop.h
x64-windows/include/H5FDdirect.h
x64-windows/include/H5FDfamily.h
x64-windows/include/H5FDhdfs.h
x64-windows/include/H5FDioc.h
x64-windows/include/H5FDlog.h
x64-windows/include/H5FDmirror.h
x64-windows/include/H5FDmpi.h
x64-windows/include/H5FDmpio.h
x64-windows/include/H5FDmulti.h
x64-windows/include/H5FDonion.h
x64-windows/include/H5FDpublic.h
x64-windows/include/H5FDros3.h
x64-windows/include/H5FDs3comms.h
x64-windows/include/H5FDsec2.h
x64-windows/include/H5FDsplitter.h
x64-windows/include/H5FDstdio.h
x64-windows/include/H5FDsubfiling.h
x64-windows/include/H5FDwindows.h
x64-windows/include/H5FaccProp.h
x64-windows/include/H5FcreatProp.h
x64-windows/include/H5File.h
x64-windows/include/H5FloatType.h
x64-windows/include/H5Fpublic.h
x64-windows/include/H5Gpublic.h
x64-windows/include/H5Group.h
x64-windows/include/H5IMpublic.h
x64-windows/include/H5IdComponent.h
x64-windows/include/H5Idevelop.h
x64-windows/include/H5Include.h
x64-windows/include/H5IntType.h
x64-windows/include/H5Ipublic.h
x64-windows/include/H5LDpublic.h
x64-windows/include/H5LTpublic.h
x64-windows/include/H5LaccProp.h
x64-windows/include/H5LcreatProp.h
x64-windows/include/H5Ldevelop.h
x64-windows/include/H5Library.h
x64-windows/include/H5Location.h
x64-windows/include/H5Lpublic.h
x64-windows/include/H5MMpublic.h
x64-windows/include/H5Mpublic.h
x64-windows/include/H5Object.h
x64-windows/include/H5OcreatProp.h
x64-windows/include/H5Opublic.h
x64-windows/include/H5PLextern.h
x64-windows/include/H5PLpublic.h
x64-windows/include/H5PTpublic.h
x64-windows/include/H5PacketTable.h
x64-windows/include/H5Ppublic.h
x64-windows/include/H5PredType.h
x64-windows/include/H5PropList.h
x64-windows/include/H5Rpublic.h
x64-windows/include/H5Spublic.h
x64-windows/include/H5StrType.h
x64-windows/include/H5TBpublic.h
x64-windows/include/H5TSdevelop.h
x64-windows/include/H5Tdevelop.h
x64-windows/include/H5Tpublic.h
x64-windows/include/H5VLconnector.h
x64-windows/include/H5VLconnector_passthru.h
x64-windows/include/H5VLnative.h
x64-windows/include/H5VLpassthru.h
x64-windows/include/H5VLpublic.h
x64-windows/include/H5VarLenType.h
x64-windows/include/H5Zdevelop.h
x64-windows/include/H5Zpublic.h
x64-windows/include/H5api_adpt.h
x64-windows/include/H5encode.h
x64-windows/include/H5overflow.h
x64-windows/include/H5pubconf.h
x64-windows/include/H5public.h
x64-windows/include/H5version.h
x64-windows/include/hdf5.h
x64-windows/include/hdf5_hl.h
x64-windows/lib/
x64-windows/lib/hdf5.lib
x64-windows/lib/hdf5_cpp.lib
x64-windows/lib/hdf5_hl.lib
x64-windows/lib/hdf5_hl_cpp.lib
x64-windows/lib/libhdf5.settings
x64-windows/lib/pkgconfig/
x64-windows/lib/pkgconfig/hdf5.pc
x64-windows/lib/pkgconfig/hdf5_cpp.pc
x64-windows/lib/pkgconfig/hdf5_hl.pc
x64-windows/lib/pkgconfig/hdf5_hl_cpp.pc
x64-windows/share/
x64-windows/share/hdf5/
x64-windows/share/hdf5/copyright
x64-windows/share/hdf5/data/
x64-windows/share/hdf5/data/RELEASE.txt
x64-windows/share/hdf5/data/USING_HDF5_CMake.txt
x64-windows/share/hdf5/data/USING_HDF5_VS.txt
x64-windows/share/hdf5/hdf5-config-version.cmake
x64-windows/share/hdf5/hdf5-config.cmake
x64-windows/share/hdf5/hdf5-targets-debug.cmake
x64-windows/share/hdf5/hdf5-targets-release.cmake
x64-windows/share/hdf5/hdf5-targets.cmake
x64-windows/share/hdf5/vcpkg-cmake-wrapper.cmake
x64-windows/share/hdf5/vcpkg.spdx.json
x64-windows/share/hdf5/vcpkg_abi_info.txt
