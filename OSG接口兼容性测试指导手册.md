# OSG接口兼容性测试指导手册

## 📋 测试概述

本手册指导您完成OSG接口在桌面和WebAssembly平台的兼容性测试，帮助识别和解决跨平台渲染问题。

### 🎯 测试目标
- 验证OSG核心渲染接口在不同平台的行为一致性
- 识别导致渲染异常的API兼容性问题
- 生成详细的测试报告用于问题分析

## 🔧 环境准备

### 必要条件
- **操作系统**: Windows 10/11
- **网络代理**: 127.0.0.1:10809 (用于下载地图瓦片)
- **浏览器**: Chrome/Firefox/Edge (支持WebAssembly)
- **Python**: 3.x (用于HTTP服务器)

### 文件结构
```
tests/osg_interface_compatibility/
├── redist_desktop/          # 桌面版测试程序
│   ├── osg_compatibility_test.exe
│   └── *.dll               # 依赖库
├── redist_wasm/            # WebAssembly版测试程序
│   ├── osg_compatibility_test.html
│   ├── osg_compatibility_test.js
│   └── osg_compatibility_test.wasm
└── 测试报告/               # 测试结果输出目录
```

## 🖥️ 桌面版测试步骤

### 第一步：准备测试环境

1. **打开命令提示符**
   ```cmd
   cd F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250714\tests\osg_interface_compatibility\redist_desktop
   ```

2. **设置网络代理环境变量**
   ```cmd
   set HTTP_PROXY=http://127.0.0.1:10809
   set HTTPS_PROXY=http://127.0.0.1:10809
   set http_proxy=http://127.0.0.1:10809
   set https_proxy=http://127.0.0.1:10809
   ```

### 第二步：运行基础测试

1. **运行基本兼容性测试**
   ```cmd
   osg_compatibility_test.exe --basic
   ```

2. **运行详细测试并生成报告**
   ```cmd
   osg_compatibility_test.exe --verbose --report desktop_test_report.html
   ```

3. **运行特定测试套件**
   ```cmd
   # 仅测试核心渲染功能
   osg_compatibility_test.exe --test CoreRendering
   
   # 仅测试缓冲区对象
   osg_compatibility_test.exe --test BufferObjects
   
   # 仅测试着色器功能
   osg_compatibility_test.exe --test Shaders
   ```

### 第三步：分析测试结果

**成功输出示例**：
```
========================================
OSG Interface Compatibility Test Framework
Platform: Windows Desktop
========================================

[INFO] Initializing graphics context...
[PASS] Graphics context created successfully
[INFO] Running CoreRenderingTests...
[PASS] BasicDrawable test completed (35.184ms)
[PASS] VBO binding test completed
[PASS] EBO binding test completed
[INFO] All tests completed successfully!

Test Summary:
- Total Tests: 8
- Passed: 8
- Failed: 0
- Duration: 156ms
```

**失败输出示例**：
```
[FAIL] Graphics context creation failed
[ERROR] Unable to create pbuffer context
[SKIP] Remaining tests skipped due to context failure
```

## 🌐 WebAssembly版测试步骤

### 第一步：启动HTTP服务器

1. **进入WebAssembly测试目录**
   ```cmd
   cd F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250714\tests\osg_interface_compatibility\redist_wasm
   ```

2. **启动Python HTTP服务器**
   ```cmd
   python -m http.server 8080
   ```
   
   **输出示例**：
   ```
   Serving HTTP on :: port 8080 (http://[::]:8080/) ...
   ```

### 第二步：配置浏览器代理

1. **Chrome浏览器配置**：
   - 打开Chrome设置 → 高级 → 系统 → 打开代理设置
   - 设置HTTP代理：127.0.0.1:10809
   - 设置HTTPS代理：127.0.0.1:10809

2. **Firefox浏览器配置**：
   - 打开Firefox设置 → 网络设置 → 设置
   - 选择"手动代理配置"
   - HTTP代理：127.0.0.1 端口：10809
   - HTTPS代理：127.0.0.1 端口：10809

### 第三步：运行WebAssembly测试

1. **打开测试页面**
   ```
   http://localhost:8080/osg_compatibility_test.html
   ```

2. **观察控制台输出**
   - 按F12打开开发者工具
   - 切换到Console标签页
   - 观察测试执行过程和结果

3. **测试交互功能**
   - 点击"Run Basic Tests"按钮
   - 点击"Run Full Test Suite"按钮
   - 点击"Generate Report"按钮

### 第四步：分析WebAssembly测试结果

**成功输出示例**：
```javascript
[LOG] OSG Interface Compatibility Test - WebAssembly
[LOG] Initializing WebGL context...
[PASS] WebGL context created successfully
[LOG] Running CoreRenderingTests...
[PASS] BasicDrawable test completed
[PASS] WebGL buffer binding test completed
[LOG] All tests completed successfully!
```

**失败输出示例**：
```javascript
[ERROR] WebGL context creation failed
[ERROR] OSG library initialization failed
[FAIL] Unable to proceed with tests
```

## 📊 测试结果对比分析

### 关键对比指标

1. **图形上下文创建**
   - 桌面版：pbuffer上下文
   - WebAssembly版：WebGL上下文

2. **缓冲区对象管理**
   - VBO绑定方式差异
   - EBO处理方式差异

3. **着色器编译**
   - GLSL版本兼容性
   - 着色器程序链接

4. **纹理操作**
   - 纹理格式支持
   - 纹理绑定方式

### 性能基准对比

| 测试项目 | 桌面版 (ms) | WebAssembly版 (ms) | 性能比率 |
|---------|------------|-------------------|---------|
| 基础绘制 | 35.184 | 待测试 | - |
| VBO绑定 | 12.456 | 待测试 | - |
| 着色器编译 | 89.123 | 待测试 | - |
| 纹理创建 | 45.678 | 待测试 | - |

## 🔍 常见问题排查

### 桌面版问题

**问题1：图形上下文创建失败**
```
解决方案：
1. 检查显卡驱动是否最新
2. 确认OpenGL支持版本
3. 尝试以管理员权限运行
```

**问题2：DLL文件缺失**
```
解决方案：
1. 确认vcpkg依赖库已正确安装
2. 检查PATH环境变量
3. 手动复制缺失的DLL文件
```

### WebAssembly版问题

**问题1：WebGL上下文创建失败**
```
解决方案：
1. 确认浏览器支持WebGL 2.0
2. 检查硬件加速是否启用
3. 尝试不同的浏览器
```

**问题2：网络请求失败**
```
解决方案：
1. 确认代理设置正确
2. 检查防火墙设置
3. 验证HTTP服务器运行状态
```

## 📝 测试报告生成

### 自动生成报告

1. **桌面版HTML报告**
   ```cmd
   osg_compatibility_test.exe --report desktop_report.html --format html
   ```

2. **WebAssembly版JSON报告**
   - 在浏览器中点击"Generate Report"
   - 报告将自动下载为JSON文件

### 手动记录关键信息

**测试环境信息**：
- 操作系统版本
- 显卡型号和驱动版本
- OSG库版本
- 编译器版本

**测试结果记录**：
- 通过的测试数量
- 失败的测试详情
- 性能数据对比
- 异常错误信息

## 🎯 下一步行动建议

### 基于测试结果的决策

1. **如果桌面版和WebAssembly版都通过**
   - 继续进行osgEarth集成测试
   - 优化性能差异

2. **如果WebAssembly版失败**
   - 分析具体失败原因
   - 实施修复建议
   - 重新测试验证

3. **如果两个版本都失败**
   - 检查基础环境配置
   - 重新编译测试程序
   - 联系技术支持

---

**测试完成后，请将测试结果和遇到的问题反馈给开发团队，以便进一步优化和改进。**
