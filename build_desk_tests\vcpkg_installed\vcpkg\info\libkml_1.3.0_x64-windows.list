x64-windows/
x64-windows/debug/
x64-windows/debug/lib/
x64-windows/debug/lib/kmlbase.lib
x64-windows/debug/lib/kmlconvenience.lib
x64-windows/debug/lib/kmldom.lib
x64-windows/debug/lib/kmlengine.lib
x64-windows/debug/lib/kmlregionator.lib
x64-windows/debug/lib/kmlxsd.lib
x64-windows/debug/lib/pkgconfig/
x64-windows/debug/lib/pkgconfig/libkml.pc
x64-windows/include/
x64-windows/include/kml/
x64-windows/include/kml/base/
x64-windows/include/kml/base/attributes.h
x64-windows/include/kml/base/color32.h
x64-windows/include/kml/base/csv_splitter.h
x64-windows/include/kml/base/date_time.h
x64-windows/include/kml/base/expat_handler.h
x64-windows/include/kml/base/expat_handler_ns.h
x64-windows/include/kml/base/expat_parser.h
x64-windows/include/kml/base/file.h
x64-windows/include/kml/base/math_util.h
x64-windows/include/kml/base/memory_file.h
x64-windows/include/kml/base/mimetypes.h
x64-windows/include/kml/base/net_cache.h
x64-windows/include/kml/base/referent.h
x64-windows/include/kml/base/string_util.h
x64-windows/include/kml/base/tempfile.h
x64-windows/include/kml/base/time_util.h
x64-windows/include/kml/base/util.h
x64-windows/include/kml/base/vec3.h
x64-windows/include/kml/base/version.h
x64-windows/include/kml/base/xml_element.h
x64-windows/include/kml/base/xml_file.h
x64-windows/include/kml/base/xml_namespaces.h
x64-windows/include/kml/base/xmlns.h
x64-windows/include/kml/base/zip_file.h
x64-windows/include/kml/convenience/
x64-windows/include/kml/convenience/atom_util.h
x64-windows/include/kml/convenience/convenience.h
x64-windows/include/kml/convenience/csv_file.h
x64-windows/include/kml/convenience/csv_parser.h
x64-windows/include/kml/convenience/element_counter.h
x64-windows/include/kml/convenience/feature_list.h
x64-windows/include/kml/convenience/google_doc_list.h
x64-windows/include/kml/convenience/google_maps_data.h
x64-windows/include/kml/convenience/google_picasa_web.h
x64-windows/include/kml/convenience/google_spreadsheets.h
x64-windows/include/kml/convenience/gpx_trk_pt_handler.h
x64-windows/include/kml/convenience/http_client.h
x64-windows/include/kml/convenience/kml_feature_list_saver.h
x64-windows/include/kml/convenience/kmz_check_links.h
x64-windows/include/kml/dom.h
x64-windows/include/kml/dom/
x64-windows/include/kml/dom/abstractlatlonbox.h
x64-windows/include/kml/dom/abstractview.h
x64-windows/include/kml/dom/atom.h
x64-windows/include/kml/dom/balloonstyle.h
x64-windows/include/kml/dom/colorstyle.h
x64-windows/include/kml/dom/container.h
x64-windows/include/kml/dom/document.h
x64-windows/include/kml/dom/element.h
x64-windows/include/kml/dom/extendeddata.h
x64-windows/include/kml/dom/feature.h
x64-windows/include/kml/dom/folder.h
x64-windows/include/kml/dom/geometry.h
x64-windows/include/kml/dom/gx_timeprimitive.h
x64-windows/include/kml/dom/gx_tour.h
x64-windows/include/kml/dom/hotspot.h
x64-windows/include/kml/dom/iconstyle.h
x64-windows/include/kml/dom/kml.h
x64-windows/include/kml/dom/kml22.h
x64-windows/include/kml/dom/kml_cast.h
x64-windows/include/kml/dom/kml_factory.h
x64-windows/include/kml/dom/kml_funcs.h
x64-windows/include/kml/dom/kml_ptr.h
x64-windows/include/kml/dom/kmldom.h
x64-windows/include/kml/dom/labelstyle.h
x64-windows/include/kml/dom/linestyle.h
x64-windows/include/kml/dom/link.h
x64-windows/include/kml/dom/liststyle.h
x64-windows/include/kml/dom/model.h
x64-windows/include/kml/dom/networklink.h
x64-windows/include/kml/dom/networklinkcontrol.h
x64-windows/include/kml/dom/object.h
x64-windows/include/kml/dom/overlay.h
x64-windows/include/kml/dom/parser.h
x64-windows/include/kml/dom/parser_observer.h
x64-windows/include/kml/dom/placemark.h
x64-windows/include/kml/dom/polystyle.h
x64-windows/include/kml/dom/region.h
x64-windows/include/kml/dom/schema.h
x64-windows/include/kml/dom/snippet.h
x64-windows/include/kml/dom/style.h
x64-windows/include/kml/dom/stylemap.h
x64-windows/include/kml/dom/styleselector.h
x64-windows/include/kml/dom/substyle.h
x64-windows/include/kml/dom/timeprimitive.h
x64-windows/include/kml/dom/vec2.h
x64-windows/include/kml/dom/visitor.h
x64-windows/include/kml/dom/visitor_driver.h
x64-windows/include/kml/dom/xal.h
x64-windows/include/kml/dom/xsd.h
x64-windows/include/kml/engine.h
x64-windows/include/kml/engine/
x64-windows/include/kml/engine/bbox.h
x64-windows/include/kml/engine/clone.h
x64-windows/include/kml/engine/engine_types.h
x64-windows/include/kml/engine/entity_mapper.h
x64-windows/include/kml/engine/feature_balloon.h
x64-windows/include/kml/engine/feature_view.h
x64-windows/include/kml/engine/feature_visitor.h
x64-windows/include/kml/engine/find.h
x64-windows/include/kml/engine/find_xml_namespaces.h
x64-windows/include/kml/engine/get_link_parents.h
x64-windows/include/kml/engine/get_links.h
x64-windows/include/kml/engine/href.h
x64-windows/include/kml/engine/id_mapper.h
x64-windows/include/kml/engine/kml_cache.h
x64-windows/include/kml/engine/kml_file.h
x64-windows/include/kml/engine/kml_stream.h
x64-windows/include/kml/engine/kml_uri.h
x64-windows/include/kml/engine/kmz_cache.h
x64-windows/include/kml/engine/kmz_file.h
x64-windows/include/kml/engine/link_util.h
x64-windows/include/kml/engine/location_util.h
x64-windows/include/kml/engine/merge.h
x64-windows/include/kml/engine/object_id_parser_observer.h
x64-windows/include/kml/engine/old_schema_parser_observer.h
x64-windows/include/kml/engine/parse_old_schema.h
x64-windows/include/kml/engine/schema_parser_observer.h
x64-windows/include/kml/engine/shared_style_parser_observer.h
x64-windows/include/kml/engine/style_inliner.h
x64-windows/include/kml/engine/style_merger.h
x64-windows/include/kml/engine/style_resolver.h
x64-windows/include/kml/engine/style_splitter.h
x64-windows/include/kml/engine/update.h
x64-windows/include/kml/regionator/
x64-windows/include/kml/regionator/feature_list_region_handler.h
x64-windows/include/kml/regionator/feature_list_regionator.h
x64-windows/include/kml/regionator/region_handler.h
x64-windows/include/kml/regionator/regionator.h
x64-windows/include/kml/regionator/regionator_qid.h
x64-windows/include/kml/regionator/regionator_util.h
x64-windows/include/kml/xsd/
x64-windows/include/kml/xsd/xsd_complex_type.h
x64-windows/include/kml/xsd/xsd_element.h
x64-windows/include/kml/xsd/xsd_file.h
x64-windows/include/kml/xsd/xsd_handler.h
x64-windows/include/kml/xsd/xsd_primitive_type.h
x64-windows/include/kml/xsd/xsd_schema.h
x64-windows/include/kml/xsd/xsd_simple_type.h
x64-windows/include/kml/xsd/xsd_type.h
x64-windows/include/kml/xsd/xsd_util.h
x64-windows/include/kml/xsd/xst_parser.h
x64-windows/lib/
x64-windows/lib/kmlbase.lib
x64-windows/lib/kmlconvenience.lib
x64-windows/lib/kmldom.lib
x64-windows/lib/kmlengine.lib
x64-windows/lib/kmlregionator.lib
x64-windows/lib/kmlxsd.lib
x64-windows/lib/pkgconfig/
x64-windows/lib/pkgconfig/libkml.pc
x64-windows/share/
x64-windows/share/libkml/
x64-windows/share/libkml/LibKMLConfig.cmake
x64-windows/share/libkml/LibKMLConfigVersion.cmake
x64-windows/share/libkml/LibKMLTargets-debug.cmake
x64-windows/share/libkml/LibKMLTargets-release.cmake
x64-windows/share/libkml/LibKMLTargets.cmake
x64-windows/share/libkml/copyright
x64-windows/share/libkml/vcpkg.spdx.json
x64-windows/share/libkml/vcpkg_abi_info.txt
