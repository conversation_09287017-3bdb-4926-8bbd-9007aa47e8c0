<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "urn:fontconfig:fonts.dtd">
<!--
  fonts-persian.conf
  To configure Persian fonts from The FarsiWeb Project.

  Copyright (C) 2005  Sharif FarsiWeb, Inc. <<EMAIL>>

  Permission to use, copy, modify, distribute, and sell this software and its
  documentation for any purpose is hereby granted without fee, provided that
  the above copyright notice appear in all copies and that both that
  copyright notice and this permission notice appear in supporting
  documentation, and that the name of <PERSON>siWeb, Inc. not be used in
  advertising or publicity pertaining to distribution of the software without
  specific, written prior permission.  Sharif FarsiWeb, Inc. makes no
  representations about the suitability of this software for any purpose.  It
  is provided "as is" without express or implied warranty.

  SHARIF FARSIWEB, INC. DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE,
  INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO
  EVENT SHALL THE AUTHOR(S) BE LIABLE FOR ANY SPECIAL, INDIRECT OR
  CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE,
  DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
  TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.

  ChangeLog:
    2005-04-03  Behdad Esfahbod:  Initial revision.
    2005-10-09  Behdad Esfahbod:  Turned off back-slant and Tahoma sections.
    2005-11-30  Behdad Esfahbod:  Set Titr susbtitution size to 24 points.
    2008	Behdad Esfahbod:  Cleanup.  Add fantasy and cursive.
  -->
<fontconfig>

<!-- Deprecated fonts are discouraged -->

	<!-- Nesf[2] is officially deprecated and has problematic tables -->
	<alias binding="same">
	  <family>Nesf</family>
	  <accept><family>Nesf2</family></accept>
	</alias>
	<alias binding="same">
	  <family>Nesf2</family>
	  <accept><family>Persian_sansserif_default</family></accept>
	</alias>

<!-- Name changes and spelling variant aliases -->

	<alias binding="same">
	  <family>Nazanin</family>
	  <accept><family>Nazli</family></accept>
	</alias>
	<alias binding="same">
	  <family>Lotus</family>
	  <accept><family>Lotoos</family></accept>
	</alias>
	<alias binding="same">
	  <family>Yaqut</family>
	  <accept><family>Yaghoot</family></accept>
	</alias>
	<alias binding="same">
	  <family>Yaghut</family>
	  <accept><family>Yaghoot</family></accept>
	</alias>
	<alias binding="same">
	  <family>Traffic</family>
	  <accept><family>Terafik</family></accept>
	</alias>
	<alias binding="same">
	  <family>Ferdowsi</family>
	  <accept><family>Ferdosi</family></accept>
	</alias>
	<alias binding="same">
	  <family>Fantezy</family>
	  <accept><family>Fantezi</family></accept>
	</alias>


<!-- Classify fonts. -->

	<!-- Persian_title class -->
	<alias binding="same">
	  <family>Jadid</family>
	  <accept><family>Persian_title</family></accept>
	</alias>
	<alias binding="same">
	  <family>Titr</family>
	  <accept><family>Persian_title</family></accept>
	</alias>

	<!-- Persian_fantasy class -->
	<alias binding="same">
	  <family>Kamran</family>
	  <accept>
	  <family>Persian_fantasy</family>
	  <family>Homa</family>
	  </accept>
	</alias>
	<alias binding="same">
	  <family>Homa</family>
	  <accept>
	  <family>Persian_fantasy</family>
	  <family>Kamran</family>
	  </accept>
	</alias>
	<alias binding="same">
	  <family>Fantezi</family>
	  <accept><family>Persian_fantasy</family></accept>
	</alias>
	<alias binding="same">
	  <family>Tabassom</family>
	  <accept><family>Persian_fantasy</family></accept>
	</alias>

	<!-- Persian_square class -->
	<alias binding="same">
	  <family>Arshia</family>
	  <accept><family>Persian_square</family></accept>
	</alias>
	<alias binding="same">
	  <family>Nasim</family>
	  <accept><family>Persian_square</family></accept>
	</alias>
	<alias binding="same">
	  <family>Elham</family>
	  <accept>
	  <family>Persian_square</family>
	  <family>Farnaz</family>
	  </accept>
	</alias>
	<alias binding="same">
	  <family>Farnaz</family>
	  <accept>
	  <family>Persian_square</family>
	  <family>Elham</family>
	  </accept>
	</alias>
	<alias binding="same">
	  <family>Sina</family>
	  <accept><family>Persian_square</family></accept>
	</alias>

<!-- Font ordering per class -->

	<!-- Persian_title class -->
	<alias binding="same">
	  <family>Persian_title</family>
	  <accept>
	  <family>Titr</family>
	  <family>Jadid</family>
	  <family>Persian_serif</family>
	  </accept>
	</alias>

	<!-- Persian_fantasy class -->
	<alias binding="same">
	  <family>Persian_fantasy</family>
	  <accept>
	  <family>Homa</family>
	  <family>Kamran</family>
	  <family>Fantezi</family>
	  <family>Tabassom</family>
	  <family>Persian_square</family>
	  </accept>
	</alias>

	<!-- Persian_square class -->
	<alias binding="same">
	  <family>Persian_square</family>
	  <accept>
	  <family>Arshia</family>
	  <family>Elham</family>
	  <family>Farnaz</family>
	  <family>Nasim</family>
	  <family>Sina</family>
	  <family>Persian_serif</family>
	  </accept>
	</alias>

<!-- Register the fonts that we actually do have -->

	<match target="scan">
		<test name="family" compare="eq" ignore-blanks="true">
			<string>Elham</string>
		</test>
		<edit name="foundry">
			<string>farsiweb</string>
		</edit>
	</match>

	<match target="scan">
		<test name="family" compare="eq" ignore-blanks="true">
			<string>Homa</string>
		</test>
		<edit name="foundry">
			<string>farsiweb</string>
		</edit>
	</match>

	<match target="scan">
		<test name="family" compare="eq" ignore-blanks="true">
			<string>Koodak</string>
		</test>
		<edit name="foundry">
			<string>farsiweb</string>
		</edit>
	</match>

	<match target="scan">
		<test name="family" compare="eq" ignore-blanks="true">
			<string>Nazli</string>
		</test>
		<edit name="foundry">
			<string>farsiweb</string>
		</edit>
	</match>

	<match target="scan">
		<test name="family" compare="eq" ignore-blanks="true">
			<string>Roya</string>
		</test>
		<edit name="foundry">
			<string>farsiweb</string>
		</edit>
	</match>

	<match target="scan">
		<test name="family" compare="eq" ignore-blanks="true">
			<string>Terafik</string>
		</test>
		<edit name="foundry">
			<string>farsiweb</string>
		</edit>
	</match>

	<match target="scan">
		<test name="family" compare="eq" ignore-blanks="true">
			<string>Titr</string>
		</test>
		<edit name="foundry">
			<string>farsiweb</string>
		</edit>
	</match>


<!-- Our fonts should oblique to the other side (TURNED-OFF) -->

	<match target="font">
		<test name="foundry">
			<!--string>farsiweb</string-->
			<string>TURNED-OFF</string>
		</test>
		<test name="foundry">
			<string>farsiweb</string>
		</test>
		<!-- check to see if the font is roman -->
		<test name="slant">
			<const>roman</const>
		</test>
		<!-- check to see if the pattern requested non-roman -->
		<test target="pattern" name="slant" compare="not_eq">
			<const>roman</const>
		</test>
		<!-- multiply the matrix to slant the font -->
		<edit name="matrix" mode="assign">
			<times>
				<name>matrix</name>
				<matrix><double>1</double><double>-0.2</double>
					<double>0</double><double>1</double>
				</matrix>
			</times>
		</edit>
		<!-- pretend the font is oblique now -->
		<edit name="slant" mode="assign">
			<const>oblique</const>
		</edit>
	</match>


<!--
  We can't hint our fonts well, so turn off hinting.
  Moreover, the bitmaps we have designed (well, they
  have designed), suck, so disable them too.
  -->

	<match target="font">
		<test name="foundry">
			<string>farsiweb</string>
		</test>
		<edit name="autohint">
			<bool>false</bool>
		</edit>
		<edit name="hinting">
			<bool>false</bool>
		</edit>
 		<edit name="embeddedbitmap">
 			<bool>false</bool>
 		</edit>
	</match>


<!-- Alias our fonts to common families -->

	<!-- Persian serif fonts -->
	<alias>
		<family>serif</family>
		<accept>
			<family>Nazli</family>
			<family>Lotoos</family>
			<family>Mitra</family>
			<family>Ferdosi</family>
			<family>Badr</family>
			<family>Zar</family>
		</accept>
	</alias>

	<!-- Persian sans-serif fonts -->
	<alias>
		<family>sans-serif</family>
		<accept>
			<family>Roya</family>
			<family>Koodak</family>
			<family>Terafik</family>
		</accept>
	</alias>

	<!-- Persian monospace fonts -->
	<alias>
		<family>monospace</family>
		<accept>
			<!-- Not really monospace -->
			<family>Terafik</family>
		</accept>
	</alias>

	<!-- Persian fantasy fonts -->
	<alias>
		<family>fantasy</family>
		<accept>
			<family>Homa</family>
			<family>Kamran</family>
			<family>Fantezi</family>
			<family>Tabassom</family>
		</accept>
	</alias>

	<!-- Persian (and Urdu) Nastaliq/cursive fonts -->
	<alias>
		<family>cursive</family>
		<accept>
			<family>IranNastaliq</family>
			<family>Nafees Nastaleeq</family>
		</accept>
	</alias>

<!-- Use Titr in titles -->

	<!-- Both serif... -->
	<match>
		<test name="family">
			<string>serif</string>
		</test>
		<test name="weight" compare="more_eq">
			<int>200</int>
		</test>
		<test name="size" compare="more_eq">
			<double>24</double>
		</test>
		<edit name="family" mode="prepend">
			<string>Titr</string>
		</edit>
	</match>

	<!-- and sans-serif. -->
	<match>
		<test name="family">
			<string>sans-serif</string>
		</test>
		<test name="weight" compare="more_eq">
			<int>200</int>
		</test>
		<test name="size" compare="more_eq">
			<double>24</double>
		</test>
		<edit name="family" mode="prepend">
			<string>Titr</string>
		</edit>
	</match>

	<!-- and more. -->
	<match>
		<test name="family">
			<string>Persian_sansserif_default</string>
		</test>
		<test name="weight" compare="more_eq">
			<int>200</int>
		</test>
		<test name="size" compare="more_eq">
			<double>24</double>
		</test>
		<edit name="family" mode="prepend" binding="same">
			<string>Titr</string>
		</edit>
	</match>


<!-- Default substituted for deprecated sans-serif fonts -->

	<match>
		<test name="family">
			<string>Persian_sansserif_default</string>
		</test>
		<edit name="family" mode="assign" binding="same">
			<string>Roya</string>
		</edit>
	</match>

</fontconfig>
