@echo off
echo ========================================
echo Building OSG Interface Compatibility Tests Only
echo ========================================

set VCPKG_ROOT=C:\dev\vcpkg
set VCPKG_TOOLCHAIN=%VCPKG_ROOT%\scripts\buildsystems\vcpkg.cmake
set BUILD_DIR=tests\osg_interface_compatibility\build_desktop
set REDIST_DIR=tests\osg_interface_compatibility\redist_desktop

echo Setting up environment...
set CMAKE_TOOLCHAIN_FILE=%VCPKG_TOOLCHAIN%
set VCPKG_TARGET_TRIPLET=x64-windows

echo Cleaning previous build...
if exist %BUILD_DIR% rmdir /s /q %BUILD_DIR%
if exist %REDIST_DIR% rmdir /s /q %REDIST_DIR%

echo Creating build directory...
mkdir %BUILD_DIR%
mkdir %REDIST_DIR%

echo ========================================
echo Building OSG Interface Compatibility Tests...
echo ========================================
cd %BUILD_DIR%

echo Configuring OSG Interface Tests...
cmake .. ^
    -DCMAKE_TOOLCHAIN_FILE=%VCPKG_TOOLCHAIN% ^
    -DVCPKG_TARGET_TRIPLET=x64-windows ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_CXX_STANDARD=20 ^
    -DCMAKE_MSVC_RUNTIME_LIBRARY=MultiThreadedDLL

if %ERRORLEVEL% neq 0 (
    echo OSG Interface Tests CMake configuration failed!
    cd ..\..\..
    pause
    exit /b 1
)

echo Building OSG Interface Tests...
cmake --build . --config Release --parallel 4

if %ERRORLEVEL% neq 0 (
    echo OSG Interface Tests build failed!
    cd ..\..\..
    pause
    exit /b 1
)

echo ========================================
echo Copying test executable to redist...
echo ========================================
cd ..\..\..
copy "%BUILD_DIR%\Release\osg_compatibility_test.exe" "%REDIST_DIR%\" 2>nul
if not exist "%REDIST_DIR%\osg_compatibility_test.exe" (
    copy "%BUILD_DIR%\osg_compatibility_test.exe" "%REDIST_DIR%\" 2>nul
)

echo ========================================
echo Copying essential DLLs...
echo ========================================
copy "%VCPKG_ROOT%\installed\x64-windows\bin\*.dll" "%REDIST_DIR%\" 2>nul

echo ========================================
echo OSG Interface Tests Build Complete!
echo ========================================
echo Build directory: %BUILD_DIR%
echo Redist directory: %REDIST_DIR%
echo Test executable: %REDIST_DIR%\osg_compatibility_test.exe
echo.
echo Ready to run OSG interface compatibility tests!

echo ========================================
echo Running Desktop OSG Interface Tests...
echo ========================================
cd %REDIST_DIR%
if exist osg_compatibility_test.exe (
    echo Running tests...
    osg_compatibility_test.exe --verbose --report desktop_osg_test_report.html
    echo.
    echo Test completed! Check desktop_osg_test_report.html for results.
) else (
    echo Test executable not found!
)

cd ..\..\..
pause
