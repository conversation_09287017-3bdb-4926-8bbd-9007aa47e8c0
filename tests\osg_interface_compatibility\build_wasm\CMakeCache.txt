# This is the CMakeCache file.
# For build in directory: f:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_wasm
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=CMAKE_ADDR2LINE-NOTFOUND

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Release

//No help, variable specified on the command line.
CMAKE_CROSSCOMPILING_EMULATOR:UNINITIALIZED=C:/dev/emsdk/node/22.16.0_64bit/bin/node.exe

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//No help, variable specified on the command line.
CMAKE_CXX_STANDARD:UNINITIALIZED=17

//`clang-scan-deps` dependency scanner
CMAKE_C_COMPILER_CLANG_SCAN_DEPS:FILEPATH=CMAKE_C_COMPILER_CLANG_SCAN_DEPS-NOTFOUND

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_wasm/CMakeFiles/pkgRedirects

//No help, variable specified on the command line.
CMAKE_FIND_ROOT_PATH:UNINITIALIZED=F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/dev/emsdk/upstream/emscripten/cache/sysroot

//Path to a program.
CMAKE_LINKER:FILEPATH=CMAKE_LINKER-NOTFOUND

//Program used to build from build.ninja files.
CMAKE_MAKE_PROGRAM:FILEPATH=C:/Windows/System32/ninja.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=C:/Program Files (x86)/Embarcadero/Studio/21.0/bin/objcopy.exe

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=CMAKE_OBJDUMP-NOTFOUND

//No help, variable specified on the command line.
CMAKE_PREFIX_PATH:UNINITIALIZED=F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=OSGInterfaceCompatibilityTests

//Path to a program.
CMAKE_READELF:FILEPATH=CMAKE_READELF-NOTFOUND

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=CMAKE_STRIP-NOTFOUND

//No help, variable specified on the command line.
CMAKE_TOOLCHAIN_FILE:UNINITIALIZED=C:\dev\emsdk\upstream\emscripten\cmake\Modules\Platform\Emscripten.cmake

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Force C/C++ compiler
EMSCRIPTEN_FORCE_COMPILERS:BOOL=ON

//If set, static library targets generate LLVM bitcode files (.bc).
// If disabled (default), UNIX ar archives (.a) are generated.
EMSCRIPTEN_GENERATE_BITCODE_STATIC_LIBRARIES:BOOL=OFF

//Path to a file.
OPENTHREADS_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//Path to a library.
OPENTHREADS_LIBRARY_DEBUG:FILEPATH=OPENTHREADS_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OPENTHREADS_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a

//Path to a file.
OSGDB_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//Path to a library.
OSGDB_LIBRARY_DEBUG:FILEPATH=OSGDB_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OSGDB_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a

//No help, variable specified on the command line.
OSGEARTH_DIR:UNINITIALIZED=F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep\lib\cmake\osgEarth

//Path to a file.
OSGGA_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//Path to a library.
OSGGA_LIBRARY_DEBUG:FILEPATH=OSGGA_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OSGGA_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a

//Value Computed by CMake
OSGInterfaceCompatibilityTests_BINARY_DIR:STATIC=F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_wasm

//Value Computed by CMake
OSGInterfaceCompatibilityTests_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
OSGInterfaceCompatibilityTests_SOURCE_DIR:STATIC=F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility

//Path to a file.
OSGUTIL_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//Path to a library.
OSGUTIL_LIBRARY_DEBUG:FILEPATH=OSGUTIL_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OSGUTIL_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a

//Path to a file.
OSGVIEWER_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//Path to a library.
OSGVIEWER_LIBRARY_DEBUG:FILEPATH=OSGVIEWER_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OSGVIEWER_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a

//No help, variable specified on the command line.
OSG_DIR:UNINITIALIZED=F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep\lib\cmake\OpenSceneGraph

//Path to a file.
OSG_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//Path to a library.
OSG_LIBRARY_DEBUG:FILEPATH=OSG_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OSG_LIBRARY_RELEASE:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=f:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility/build_wasm
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=26
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=4
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_CLANG_SCAN_DEPS
CMAKE_C_COMPILER_CLANG_SCAN_DEPS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake-gui.exe
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250714/tests/osg_interface_compatibility
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-3.26
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//If true, we are targeting Emscripten output.
EMSCRIPTEN:INTERNAL=1
//Details about finding OpenSceneGraph
FIND_PACKAGE_MESSAGE_DETAILS_OpenSceneGraph:INTERNAL=[F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a;F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a;F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a;F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a;F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a;F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a][F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include][TRUE][TRUE][TRUE][TRUE][TRUE][TRUE][v3.7.0()]
//Details about finding OpenThreads
FIND_PACKAGE_MESSAGE_DETAILS_OpenThreads:INTERNAL=[F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a][F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include][v()]
//Details about finding osg
FIND_PACKAGE_MESSAGE_DETAILS_osg:INTERNAL=[F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a][F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include][v()]
//Details about finding osgDB
FIND_PACKAGE_MESSAGE_DETAILS_osgDB:INTERNAL=[F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a][F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include][v()]
//Details about finding osgGA
FIND_PACKAGE_MESSAGE_DETAILS_osgGA:INTERNAL=[F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a][F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include][v()]
//Details about finding osgUtil
FIND_PACKAGE_MESSAGE_DETAILS_osgUtil:INTERNAL=[F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a][F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include][v()]
//Details about finding osgViewer
FIND_PACKAGE_MESSAGE_DETAILS_osgViewer:INTERNAL=[F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a][F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include][v()]
//The version of OSG which was detected
OPENSCENEGRAPH_VERSION:INTERNAL=3.7.0
//ADVANCED property for variable: OPENTHREADS_LIBRARY_DEBUG
OPENTHREADS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENTHREADS_LIBRARY_RELEASE
OPENTHREADS_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGDB_LIBRARY_DEBUG
OSGDB_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGDB_LIBRARY_RELEASE
OSGDB_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGGA_LIBRARY_DEBUG
OSGGA_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGGA_LIBRARY_RELEASE
OSGGA_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGUTIL_LIBRARY_DEBUG
OSGUTIL_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGUTIL_LIBRARY_RELEASE
OSGUTIL_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGVIEWER_LIBRARY_DEBUG
OSGVIEWER_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGVIEWER_LIBRARY_RELEASE
OSGVIEWER_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSG_LIBRARY_DEBUG
OSG_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSG_LIBRARY_RELEASE
OSG_LIBRARY_RELEASE-ADVANCED:INTERNAL=1

