﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{793D7ADD-ED0E-3474-AB41-5BD56F170DEE}"
	ProjectSection(ProjectDependencies) = postProject
		{652915D1-4347-373F-BB4A-0ECB48E712DD} = {652915D1-4347-373F-BB4A-0ECB48E712DD}
		{9D66E796-CD22-3EFF-B81F-BE514DDA352D} = {9D66E796-CD22-3EFF-B81F-BE514DDA352D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{E80C8817-950A-3B20-A039-0F9088D9D95C}"
	ProjectSection(ProjectDependencies) = postProject
		{793D7ADD-ED0E-3474-AB41-5BD56F170DEE} = {793D7ADD-ED0E-3474-AB41-5BD56F170DEE}
		{652915D1-4347-373F-BB4A-0ECB48E712DD} = {652915D1-4347-373F-BB4A-0ECB48E712DD}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{652915D1-4347-373F-BB4A-0ECB48E712DD}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "osg_compatibility_test", "osg_compatibility_test.vcxproj", "{9D66E796-CD22-3EFF-B81F-BE514DDA352D}"
	ProjectSection(ProjectDependencies) = postProject
		{652915D1-4347-373F-BB4A-0ECB48E712DD} = {652915D1-4347-373F-BB4A-0ECB48E712DD}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "run_compatibility_tests", "run_compatibility_tests.vcxproj", "{37AB3E85-8C69-32D6-BD53-15A153DF8318}"
	ProjectSection(ProjectDependencies) = postProject
		{652915D1-4347-373F-BB4A-0ECB48E712DD} = {652915D1-4347-373F-BB4A-0ECB48E712DD}
		{9D66E796-CD22-3EFF-B81F-BE514DDA352D} = {9D66E796-CD22-3EFF-B81F-BE514DDA352D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "run_compatibility_tests_verbose", "run_compatibility_tests_verbose.vcxproj", "{9225733B-0401-31E4-AE6D-5AB8E8C33524}"
	ProjectSection(ProjectDependencies) = postProject
		{652915D1-4347-373F-BB4A-0ECB48E712DD} = {652915D1-4347-373F-BB4A-0ECB48E712DD}
		{9D66E796-CD22-3EFF-B81F-BE514DDA352D} = {9D66E796-CD22-3EFF-B81F-BE514DDA352D}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{793D7ADD-ED0E-3474-AB41-5BD56F170DEE}.Debug|x64.ActiveCfg = Debug|x64
		{793D7ADD-ED0E-3474-AB41-5BD56F170DEE}.Debug|x64.Build.0 = Debug|x64
		{793D7ADD-ED0E-3474-AB41-5BD56F170DEE}.Release|x64.ActiveCfg = Release|x64
		{793D7ADD-ED0E-3474-AB41-5BD56F170DEE}.Release|x64.Build.0 = Release|x64
		{793D7ADD-ED0E-3474-AB41-5BD56F170DEE}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{793D7ADD-ED0E-3474-AB41-5BD56F170DEE}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{793D7ADD-ED0E-3474-AB41-5BD56F170DEE}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{793D7ADD-ED0E-3474-AB41-5BD56F170DEE}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{E80C8817-950A-3B20-A039-0F9088D9D95C}.Debug|x64.ActiveCfg = Debug|x64
		{E80C8817-950A-3B20-A039-0F9088D9D95C}.Release|x64.ActiveCfg = Release|x64
		{E80C8817-950A-3B20-A039-0F9088D9D95C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E80C8817-950A-3B20-A039-0F9088D9D95C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{652915D1-4347-373F-BB4A-0ECB48E712DD}.Debug|x64.ActiveCfg = Debug|x64
		{652915D1-4347-373F-BB4A-0ECB48E712DD}.Debug|x64.Build.0 = Debug|x64
		{652915D1-4347-373F-BB4A-0ECB48E712DD}.Release|x64.ActiveCfg = Release|x64
		{652915D1-4347-373F-BB4A-0ECB48E712DD}.Release|x64.Build.0 = Release|x64
		{652915D1-4347-373F-BB4A-0ECB48E712DD}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{652915D1-4347-373F-BB4A-0ECB48E712DD}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{652915D1-4347-373F-BB4A-0ECB48E712DD}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{652915D1-4347-373F-BB4A-0ECB48E712DD}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{9D66E796-CD22-3EFF-B81F-BE514DDA352D}.Debug|x64.ActiveCfg = Debug|x64
		{9D66E796-CD22-3EFF-B81F-BE514DDA352D}.Debug|x64.Build.0 = Debug|x64
		{9D66E796-CD22-3EFF-B81F-BE514DDA352D}.Release|x64.ActiveCfg = Release|x64
		{9D66E796-CD22-3EFF-B81F-BE514DDA352D}.Release|x64.Build.0 = Release|x64
		{9D66E796-CD22-3EFF-B81F-BE514DDA352D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9D66E796-CD22-3EFF-B81F-BE514DDA352D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{9D66E796-CD22-3EFF-B81F-BE514DDA352D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9D66E796-CD22-3EFF-B81F-BE514DDA352D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{37AB3E85-8C69-32D6-BD53-15A153DF8318}.Debug|x64.ActiveCfg = Debug|x64
		{37AB3E85-8C69-32D6-BD53-15A153DF8318}.Release|x64.ActiveCfg = Release|x64
		{37AB3E85-8C69-32D6-BD53-15A153DF8318}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{37AB3E85-8C69-32D6-BD53-15A153DF8318}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9225733B-0401-31E4-AE6D-5AB8E8C33524}.Debug|x64.ActiveCfg = Debug|x64
		{9225733B-0401-31E4-AE6D-5AB8E8C33524}.Release|x64.ActiveCfg = Release|x64
		{9225733B-0401-31E4-AE6D-5AB8E8C33524}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9225733B-0401-31E4-AE6D-5AB8E8C33524}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {03837324-84C3-31F4-902B-38E793F24810}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
